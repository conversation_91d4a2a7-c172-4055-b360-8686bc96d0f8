/**
 * React Router v6.16.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("@remix-run/router")):"function"==typeof define&&define.amd?define(["exports","react","@remix-run/router"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactRouter={},e.React,e.RemixRouter)}(this,(function(e,t,r){"use strict";function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=n(t);function o(){return o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},o.apply(this,arguments)}const i=a.createContext(null),u=a.createContext(null),l=a.createContext(null),s=a.createContext(null),c=a.createContext(null),d=a.createContext({outlet:null,matches:[],isDataRoute:!1}),p=a.createContext(null);function m(){return null!=a.useContext(c)}function h(){return m()||r.UNSAFE_invariant(!1),a.useContext(c).location}function f(e){a.useContext(s).static||a.useLayoutEffect(e)}function v(){let{isDataRoute:e}=a.useContext(d);return e?function(){let{router:e}=A(_.UseNavigateStable),t=j(N.UseNavigateStable),r=a.useRef(!1);return f((()=>{r.current=!0})),a.useCallback((function(n,a){void 0===a&&(a={}),r.current&&("number"==typeof n?e.navigate(n):e.navigate(n,o({fromRouteId:t},a)))}),[e,t])}():function(){m()||r.UNSAFE_invariant(!1);let e=a.useContext(i),{basename:t,navigator:n}=a.useContext(s),{matches:o}=a.useContext(d),{pathname:u}=h(),l=JSON.stringify(r.UNSAFE_getPathContributingMatches(o).map((e=>e.pathnameBase))),c=a.useRef(!1);return f((()=>{c.current=!0})),a.useCallback((function(a,o){if(void 0===o&&(o={}),!c.current)return;if("number"==typeof a)return void n.go(a);let i=r.resolveTo(a,JSON.parse(l),u,"path"===o.relative);null==e&&"/"!==t&&(i.pathname="/"===i.pathname?t:r.joinPaths([t,i.pathname])),(o.replace?n.replace:n.push)(i,o.state,o)}),[t,n,l,u,e])}()}const g=a.createContext(null);function E(e){let t=a.useContext(d).outlet;return t?a.createElement(g.Provider,{value:e},t):t}function b(e,t){let{relative:n}=void 0===t?{}:t,{matches:o}=a.useContext(d),{pathname:i}=h(),u=JSON.stringify(r.UNSAFE_getPathContributingMatches(o).map((e=>e.pathnameBase)));return a.useMemo((()=>r.resolveTo(e,JSON.parse(u),i,"path"===n)),[e,u,i,n])}function y(e,t){return R(e,t)}function R(e,t,n){m()||r.UNSAFE_invariant(!1);let{navigator:i}=a.useContext(s),{matches:u}=a.useContext(d),l=u[u.length-1],p=l?l.params:{};!l||l.pathname;let f=l?l.pathnameBase:"/";l&&l.route;let v,g=h();if(t){var E;let e="string"==typeof t?r.parsePath(t):t;"/"===f||(null==(E=e.pathname)?void 0:E.startsWith(f))||r.UNSAFE_invariant(!1),v=e}else v=g;let b=v.pathname||"/",y="/"===f?b:b.slice(f.length)||"/",R=r.matchRoutes(e,{pathname:y}),C=S(R&&R.map((e=>Object.assign({},e,{params:Object.assign({},p,e.params),pathname:r.joinPaths([f,i.encodeLocation?i.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?f:r.joinPaths([f,i.encodeLocation?i.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),u,n);return t&&C?a.createElement(c.Provider,{value:{location:o({pathname:"/",search:"",hash:"",state:null,key:"default"},v),navigationType:r.Action.Pop}},C):C}function C(){let e=D(),t=r.isRouteErrorResponse(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:o},n):null,null)}const P=a.createElement(C,null);class x extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error||t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error?a.createElement(d.Provider,{value:this.props.routeContext},a.createElement(p.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function U(e){let{routeContext:t,match:r,children:n}=e,o=a.useContext(i);return o&&o.static&&o.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=r.route.id),a.createElement(d.Provider,{value:t},n)}function S(e,t,n){var o;if(void 0===t&&(t=[]),void 0===n&&(n=null),null==e){var i;if(null==(i=n)||!i.errors)return null;e=n.matches}let u=e,l=null==(o=n)?void 0:o.errors;if(null!=l){let e=u.findIndex((e=>e.route.id&&(null==l?void 0:l[e.route.id])));e>=0||r.UNSAFE_invariant(!1),u=u.slice(0,Math.min(u.length,e+1))}return u.reduceRight(((e,r,o)=>{let i=r.route.id?null==l?void 0:l[r.route.id]:null,s=null;n&&(s=r.route.errorElement||P);let c=t.concat(u.slice(0,o+1)),d=()=>{let t;return t=i?s:r.route.Component?a.createElement(r.route.Component,null):r.route.element?r.route.element:e,a.createElement(U,{match:r,routeContext:{outlet:e,matches:c,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===o)?a.createElement(x,{location:n.location,revalidation:n.revalidation,component:s,error:i,children:d(),routeContext:{outlet:null,matches:c,isDataRoute:!0}}):d()}),null)}var _=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(_||{}),N=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(N||{});function A(e){let t=a.useContext(i);return t||r.UNSAFE_invariant(!1),t}function O(e){let t=a.useContext(u);return t||r.UNSAFE_invariant(!1),t}function j(e){let t=function(e){let t=a.useContext(d);return t||r.UNSAFE_invariant(!1),t}(),n=t.matches[t.matches.length-1];return n.route.id||r.UNSAFE_invariant(!1),n.route.id}function D(){var e;let t=a.useContext(p),r=O(N.UseRouteError),n=j(N.UseRouteError);return t||(null==(e=r.errors)?void 0:e[n])}function F(){let e=a.useContext(l);return null==e?void 0:e._data}let B=0;const k=a.startTransition;function L(e){let{routes:t,state:r}=e;return R(t,void 0,r)}function M(e){r.UNSAFE_invariant(!1)}function T(e){let{basename:t="/",children:n=null,location:o,navigationType:i=r.Action.Pop,navigator:u,static:l=!1}=e;m()&&r.UNSAFE_invariant(!1);let d=t.replace(/^\/*/,"/"),p=a.useMemo((()=>({basename:d,navigator:u,static:l})),[d,u,l]);"string"==typeof o&&(o=r.parsePath(o));let{pathname:h="/",search:f="",hash:v="",state:g=null,key:E="default"}=o,b=a.useMemo((()=>{let e=r.stripBasename(h,d);return null==e?null:{location:{pathname:e,search:f,hash:v,state:g,key:E},navigationType:i}}),[d,h,f,v,g,E,i]);return null==b?null:a.createElement(s.Provider,{value:p},a.createElement(c.Provider,{children:n,value:b}))}var I=function(e){return e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error",e}(I||{});const w=new Promise((()=>{}));class J extends a.Component{constructor(e){super(e),this.state={error:null}}static getDerivedStateFromError(e){return{error:e}}componentDidCatch(e,t){console.error("<Await> caught the following error during render",e,t)}render(){let{children:e,errorElement:t,resolve:n}=this.props,o=null,i=I.pending;if(n instanceof Promise)if(this.state.error){i=I.error;let e=this.state.error;o=Promise.reject().catch((()=>{})),Object.defineProperty(o,"_tracked",{get:()=>!0}),Object.defineProperty(o,"_error",{get:()=>e})}else n._tracked?(o=n,i=void 0!==o._error?I.error:void 0!==o._data?I.success:I.pending):(i=I.pending,Object.defineProperty(n,"_tracked",{get:()=>!0}),o=n.then((e=>Object.defineProperty(n,"_data",{get:()=>e})),(e=>Object.defineProperty(n,"_error",{get:()=>e}))));else i=I.success,o=Promise.resolve(),Object.defineProperty(o,"_tracked",{get:()=>!0}),Object.defineProperty(o,"_data",{get:()=>n});if(i===I.error&&o._error instanceof r.AbortedDeferredError)throw w;if(i===I.error&&!t)throw o._error;if(i===I.error)return a.createElement(l.Provider,{value:o,children:t});if(i===I.success)return a.createElement(l.Provider,{value:o,children:e});throw o}}function H(e){let{children:t}=e,r=F(),n="function"==typeof t?t(r):t;return a.createElement(a.Fragment,null,n)}function z(e,t){void 0===t&&(t=[]);let n=[];return a.Children.forEach(e,((e,o)=>{if(!a.isValidElement(e))return;let i=[...t,o];if(e.type===a.Fragment)return void n.push.apply(n,z(e.props.children,i));e.type!==M&&r.UNSAFE_invariant(!1),e.props.index&&e.props.children&&r.UNSAFE_invariant(!1);let u={id:e.props.id||i.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(u.children=z(e.props.children,i)),n.push(u)})),n}function q(e){let t={hasErrorBoundary:null!=e.ErrorBoundary||null!=e.errorElement};return e.Component&&Object.assign(t,{element:a.createElement(e.Component),Component:void 0}),e.ErrorBoundary&&Object.assign(t,{errorElement:a.createElement(e.ErrorBoundary),ErrorBoundary:void 0}),t}Object.defineProperty(e,"AbortedDeferredError",{enumerable:!0,get:function(){return r.AbortedDeferredError}}),Object.defineProperty(e,"NavigationType",{enumerable:!0,get:function(){return r.Action}}),Object.defineProperty(e,"createPath",{enumerable:!0,get:function(){return r.createPath}}),Object.defineProperty(e,"defer",{enumerable:!0,get:function(){return r.defer}}),Object.defineProperty(e,"generatePath",{enumerable:!0,get:function(){return r.generatePath}}),Object.defineProperty(e,"isRouteErrorResponse",{enumerable:!0,get:function(){return r.isRouteErrorResponse}}),Object.defineProperty(e,"json",{enumerable:!0,get:function(){return r.json}}),Object.defineProperty(e,"matchPath",{enumerable:!0,get:function(){return r.matchPath}}),Object.defineProperty(e,"matchRoutes",{enumerable:!0,get:function(){return r.matchRoutes}}),Object.defineProperty(e,"parsePath",{enumerable:!0,get:function(){return r.parsePath}}),Object.defineProperty(e,"redirect",{enumerable:!0,get:function(){return r.redirect}}),Object.defineProperty(e,"redirectDocument",{enumerable:!0,get:function(){return r.redirectDocument}}),Object.defineProperty(e,"resolvePath",{enumerable:!0,get:function(){return r.resolvePath}}),e.Await=function(e){let{children:t,errorElement:r,resolve:n}=e;return a.createElement(J,{resolve:n,errorElement:r},a.createElement(H,null,t))},e.MemoryRouter=function(e){let{basename:t,children:n,initialEntries:o,initialIndex:i,future:u}=e,l=a.useRef();null==l.current&&(l.current=r.createMemoryHistory({initialEntries:o,initialIndex:i,v5Compat:!0}));let s=l.current,[c,d]=a.useState({action:s.action,location:s.location}),{v7_startTransition:p}=u||{},m=a.useCallback((e=>{p&&k?k((()=>d(e))):d(e)}),[d,p]);return a.useLayoutEffect((()=>s.listen(m)),[s,m]),a.createElement(T,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:s})},e.Navigate=function(e){let{to:t,replace:n,state:o,relative:i}=e;m()||r.UNSAFE_invariant(!1);let{matches:u}=a.useContext(d),{pathname:l}=h(),s=v(),c=r.resolveTo(t,r.UNSAFE_getPathContributingMatches(u).map((e=>e.pathnameBase)),l,"path"===i),p=JSON.stringify(c);return a.useEffect((()=>s(JSON.parse(p),{replace:n,state:o,relative:i})),[s,p,i,n,o]),null},e.Outlet=function(e){return E(e.context)},e.Route=M,e.Router=T,e.RouterProvider=function(e){let{fallbackElement:t,router:r,future:n}=e,[o,l]=a.useState(r.state),{v7_startTransition:s}=n||{},c=a.useCallback((e=>{s&&k?k((()=>l(e))):l(e)}),[l,s]);a.useLayoutEffect((()=>r.subscribe(c)),[r,c]);let d=a.useMemo((()=>({createHref:r.createHref,encodeLocation:r.encodeLocation,go:e=>r.navigate(e),push:(e,t,n)=>r.navigate(e,{state:t,preventScrollReset:null==n?void 0:n.preventScrollReset}),replace:(e,t,n)=>r.navigate(e,{replace:!0,state:t,preventScrollReset:null==n?void 0:n.preventScrollReset})})),[r]),p=r.basename||"/",m=a.useMemo((()=>({router:r,navigator:d,static:!1,basename:p})),[r,d,p]);return a.createElement(a.Fragment,null,a.createElement(i.Provider,{value:m},a.createElement(u.Provider,{value:o},a.createElement(T,{basename:p,location:o.location,navigationType:o.historyAction,navigator:d},o.initialized?a.createElement(L,{routes:r.routes,state:o}):t))),null)},e.Routes=function(e){let{children:t,location:r}=e;return y(z(t),r)},e.UNSAFE_DataRouterContext=i,e.UNSAFE_DataRouterStateContext=u,e.UNSAFE_LocationContext=c,e.UNSAFE_NavigationContext=s,e.UNSAFE_RouteContext=d,e.UNSAFE_mapRouteProperties=q,e.UNSAFE_useRouteId=function(){return j(N.UseRouteId)},e.UNSAFE_useRoutesImpl=R,e.createMemoryRouter=function(e,t){return r.createRouter({basename:null==t?void 0:t.basename,future:o({},null==t?void 0:t.future,{v7_prependBasename:!0}),history:r.createMemoryHistory({initialEntries:null==t?void 0:t.initialEntries,initialIndex:null==t?void 0:t.initialIndex}),hydrationData:null==t?void 0:t.hydrationData,routes:e,mapRouteProperties:q}).initialize()},e.createRoutesFromChildren=z,e.createRoutesFromElements=z,e.renderMatches=function(e){return S(e)},e.unstable_useBlocker=function(e){let{router:t,basename:n}=A(_.UseBlocker),i=O(N.UseBlocker),[u,l]=a.useState(""),s=a.useCallback((t=>{if("function"!=typeof e)return!!e;if("/"===n)return e(t);let{currentLocation:a,nextLocation:i,historyAction:u}=t;return e({currentLocation:o({},a,{pathname:r.stripBasename(a.pathname,n)||a.pathname}),nextLocation:o({},i,{pathname:r.stripBasename(i.pathname,n)||i.pathname}),historyAction:u})}),[n,e]);return a.useEffect((()=>{let e=String(++B);return l(e),()=>t.deleteBlocker(e)}),[t]),a.useEffect((()=>{""!==u&&t.getBlocker(u,s)}),[t,u,s]),u&&i.blockers.has(u)?i.blockers.get(u):r.IDLE_BLOCKER},e.useActionData=function(){let e=O(N.UseActionData);return a.useContext(d)||r.UNSAFE_invariant(!1),Object.values((null==e?void 0:e.actionData)||{})[0]},e.useAsyncError=function(){let e=a.useContext(l);return null==e?void 0:e._error},e.useAsyncValue=F,e.useHref=function(e,t){let{relative:n}=void 0===t?{}:t;m()||r.UNSAFE_invariant(!1);let{basename:o,navigator:i}=a.useContext(s),{hash:u,pathname:l,search:c}=b(e,{relative:n}),d=l;return"/"!==o&&(d="/"===l?o:r.joinPaths([o,l])),i.createHref({pathname:d,search:c,hash:u})},e.useInRouterContext=m,e.useLoaderData=function(){let e=O(N.UseLoaderData),t=j(N.UseLoaderData);if(!e.errors||null==e.errors[t])return e.loaderData[t];console.error("You cannot `useLoaderData` in an errorElement (routeId: "+t+")")},e.useLocation=h,e.useMatch=function(e){m()||r.UNSAFE_invariant(!1);let{pathname:t}=h();return a.useMemo((()=>r.matchPath(e,t)),[t,e])},e.useMatches=function(){let{matches:e,loaderData:t}=O(N.UseMatches);return a.useMemo((()=>e.map((e=>r.UNSAFE_convertRouteMatchToUiMatch(e,t)))),[e,t])},e.useNavigate=v,e.useNavigation=function(){return O(N.UseNavigation).navigation},e.useNavigationType=function(){return a.useContext(c).navigationType},e.useOutlet=E,e.useOutletContext=function(){return a.useContext(g)},e.useParams=function(){let{matches:e}=a.useContext(d),t=e[e.length-1];return t?t.params:{}},e.useResolvedPath=b,e.useRevalidator=function(){let e=A(_.UseRevalidator),t=O(N.UseRevalidator);return a.useMemo((()=>({revalidate:e.router.revalidate,state:t.revalidation})),[e.router.revalidate,t.revalidation])},e.useRouteError=D,e.useRouteLoaderData=function(e){return O(N.UseRouteLoaderData).loaderData[e]},e.useRoutes=y,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react-router.production.min.js.map
