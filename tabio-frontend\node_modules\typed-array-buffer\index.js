'use strict';

var GetIntrinsic = require('get-intrinsic');

var $TypeError = GetIntrinsic('%TypeError%');

var callBound = require('call-bind/callBound');

var $typedArrayBuffer = callBound('TypedArray.prototype.buffer', true);

var isTypedArray = require('is-typed-array');

// node <= 0.10, < 0.11.4 has a nonconfigurable own property instead of a prototype getter
module.exports = $typedArrayBuffer || function typedArrayBuffer(x) {
	if (!isTypedArray(x)) {
		throw new $TypeError('Not a Typed Array');
	}
	return x.buffer;
};
