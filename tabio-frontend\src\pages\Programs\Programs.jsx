const Programs = () => {
  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 py-20 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="mb-8">
              <span className="inline-block bg-white bg-opacity-20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium border border-white border-opacity-30">
                Empowering Communities Since 2004
              </span>
            </div>
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              Our <span className="text-blue-300">Programs</span>
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 leading-relaxed max-w-3xl mx-auto">
              Transforming lives through education, skills development, and community empowerment.
              Discover our comprehensive programs designed to build a better future for Nigerian youth.
            </p>

            <div className="mt-12">
              <div className="animate-bounce">
                <i className="fas fa-chevron-down text-white text-2xl"></i>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Program - Catch Them Young */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="order-2 lg:order-1">
              <div className="relative">
                <img
                  src="/tabio-cms-images/cty-golive/0Q1A1843.JPG"
                  alt="Catch Them Young Program"
                  className="rounded-2xl shadow-2xl w-full"
                />
                <div className="absolute -bottom-6 -right-6 bg-blue-600 text-white p-6 rounded-2xl shadow-xl">
                  <div className="text-center">
                    <div className="text-3xl font-bold">2023</div>
                    <div className="text-sm">Latest Edition</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="order-1 lg:order-2">
              <div className="mb-6">
                <span className="inline-block bg-blue-100 text-blue-600 px-4 py-2 rounded-full text-sm font-medium">
                  Flagship Program
                </span>
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6 leading-tight">
                Catch Them <span className="text-blue-600">Young</span>
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                Our flagship two-week intensive training program for teenagers aged 13-19.
                Participants dive deep into in-demand skills that prepare them for successful careers
                and entrepreneurship opportunities.
              </p>

              <div className="mb-8">
                <h3 className="text-xl font-semibold text-gray-800 mb-4">Skills Covered:</h3>
                <div className="grid grid-cols-2 gap-3">
                  <div className="flex items-center">
                    <i className="fas fa-check-circle text-blue-600 mr-2"></i>
                    <span className="text-gray-700">Fashion Designing</span>
                  </div>
                  <div className="flex items-center">
                    <i className="fas fa-check-circle text-blue-600 mr-2"></i>
                    <span className="text-gray-700">Electrical Engineering</span>
                  </div>
                  <div className="flex items-center">
                    <i className="fas fa-check-circle text-blue-600 mr-2"></i>
                    <span className="text-gray-700">Computer Engineering</span>
                  </div>
                  <div className="flex items-center">
                    <i className="fas fa-check-circle text-blue-600 mr-2"></i>
                    <span className="text-gray-700">Graphics Design</span>
                  </div>
                  <div className="flex items-center">
                    <i className="fas fa-check-circle text-blue-600 mr-2"></i>
                    <span className="text-gray-700">Web Design</span>
                  </div>
                  <div className="flex items-center">
                    <i className="fas fa-check-circle text-blue-600 mr-2"></i>
                    <span className="text-gray-700">Hairdressing</span>
                  </div>
                  <div className="flex items-center">
                    <i className="fas fa-check-circle text-blue-600 mr-2"></i>
                    <span className="text-gray-700">Catering</span>
                  </div>
                  <div className="flex items-center">
                    <i className="fas fa-check-circle text-blue-600 mr-2"></i>
                    <span className="text-gray-700">Makeup Artistry</span>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-2">2023 Success Story</h4>
                <p className="text-gray-600 text-sm">
                  Our most successful edition yet with record participation and outstanding feedback.
                  Participants received certificates and many have already started applying their new skills.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* All Programs Grid */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block bg-blue-100 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
              Our Impact Areas
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Comprehensive <span className="text-blue-600">Programs</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              From education support to skills development, our programs address multiple aspects
              of youth and community development across Nigeria.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Education Support Program */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6">
                <i className="fas fa-graduation-cap text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Education Support</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Comprehensive educational assistance including SSCE exam sponsorship,
                tertiary institution tuition financing, and academic mentorship programs.
              </p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-green-500 mr-2"></i>
                  SSCE Exam Sponsorship
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-green-500 mr-2"></i>
                  University Tuition Support
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-green-500 mr-2"></i>
                  Academic Mentorship
                </div>
              </div>
              <div className="flex items-center text-green-600 font-semibold">
                <span>500+ Students Supported</span>
                <i className="fas fa-arrow-right ml-2"></i>
              </div>
            </div>

            {/* Women Empowerment Program */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
                <i className="fas fa-hands-helping text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Women Empowerment</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                SME financing and business development support for women and widows,
                enabling them to build sustainable livelihoods and support their families.
              </p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-purple-500 mr-2"></i>
                  Micro-finance Support
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-purple-500 mr-2"></i>
                  Business Training
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-purple-500 mr-2"></i>
                  Mentorship Programs
                </div>
              </div>
              <div className="flex items-center text-purple-600 font-semibold">
                <span>50+ Women Empowered</span>
                <i className="fas fa-arrow-right ml-2"></i>
              </div>
            </div>

            {/* Career Development Program */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6">
                <i className="fas fa-briefcase text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Career Development</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Professional development sessions featuring industry experts sharing insights
                on career choices, management skills, and professional growth.
              </p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-blue-500 mr-2"></i>
                  Career Guidance Sessions
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-blue-500 mr-2"></i>
                  Industry Expert Talks
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-blue-500 mr-2"></i>
                  Professional Networking
                </div>
              </div>
              <div className="flex items-center text-blue-600 font-semibold">
                <span>Regular Sessions</span>
                <i className="fas fa-arrow-right ml-2"></i>
              </div>
            </div>

            {/* Orphanage Support Program */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl flex items-center justify-center mb-6">
                <i className="fas fa-heart text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Orphanage Support</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Comprehensive support for orphanage homes including educational materials,
                food supplies, and infrastructure development across Lagos and beyond.
              </p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-red-500 mr-2"></i>
                  Educational Materials
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-red-500 mr-2"></i>
                  Food & Supplies
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-red-500 mr-2"></i>
                  Infrastructure Support
                </div>
              </div>
              <div className="flex items-center text-red-600 font-semibold">
                <span>Multiple Homes Supported</span>
                <i className="fas fa-arrow-right ml-2"></i>
              </div>
            </div>

            {/* Skills Development Workshops */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6">
                <i className="fas fa-tools text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Skills Workshops</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Regular workshops and training sessions covering various technical and
                vocational skills to enhance employability and entrepreneurship.
              </p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-orange-500 mr-2"></i>
                  Technical Skills Training
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-orange-500 mr-2"></i>
                  Vocational Development
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-orange-500 mr-2"></i>
                  Entrepreneurship Training
                </div>
              </div>
              <div className="flex items-center text-orange-600 font-semibold">
                <span>Year-round Programs</span>
                <i className="fas fa-arrow-right ml-2"></i>
              </div>
            </div>

            {/* Prayer & Spiritual Development */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6">
                <i className="fas fa-praying-hands text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Spiritual Development</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Quarterly and annual prayer retreats that have blessed many across Nigeria
                and beyond, fostering spiritual growth and community building.
              </p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-indigo-500 mr-2"></i>
                  Quarterly Prayer Retreats
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-indigo-500 mr-2"></i>
                  Annual Conferences
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <i className="fas fa-check text-indigo-500 mr-2"></i>
                  Community Fellowship
                </div>
              </div>
              <div className="flex items-center text-indigo-600 font-semibold">
                <span>Nationwide Impact</span>
                <i className="fas fa-arrow-right ml-2"></i>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Career Development Topics Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block bg-green-100 text-green-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
              Professional Development
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Career Development <span className="text-green-600">Topics</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Our career sessions feature industry experts sharing valuable insights on professional growth,
              career choices, and success strategies for young professionals.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border border-blue-200">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-brain text-white text-xl"></i>
                </div>
                <h3 className="text-2xl font-bold text-gray-800">Management & Focus</h3>
              </div>
              <h4 className="text-lg font-semibold text-blue-700 mb-3">
                "Management and Overcoming Distractions"
              </h4>
              <p className="text-gray-600 leading-relaxed">
                Learn essential management skills and strategies to overcome distractions
                in today's fast-paced world. Develop focus and productivity techniques
                that lead to professional success.
              </p>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-8 border border-green-200">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-route text-white text-xl"></i>
                </div>
                <h3 className="text-2xl font-bold text-gray-800">Career Choices</h3>
              </div>
              <h4 className="text-lg font-semibold text-green-700 mb-3">
                "Career Choices and Their Future Impact on Youth"
              </h4>
              <p className="text-gray-600 leading-relaxed">
                Understand how career decisions made today shape your future.
                Get guidance on choosing the right career path and making informed
                decisions about your professional journey.
              </p>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-8 border border-purple-200">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-graduation-cap text-white text-xl"></i>
                </div>
                <h3 className="text-2xl font-bold text-gray-800">Education Impact</h3>
              </div>
              <h4 className="text-lg font-semibold text-purple-700 mb-3">
                "The Importance of Education in Shaping Youth's Future"
              </h4>
              <p className="text-gray-600 leading-relaxed">
                Discover how education serves as the foundation for personal and
                professional growth. Learn about different educational pathways
                and their impact on career opportunities.
              </p>
            </div>

            <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-8 border border-orange-200">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-orange-600 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-trophy text-white text-xl"></i>
                </div>
                <h3 className="text-2xl font-bold text-gray-800">Success Principles</h3>
              </div>
              <h4 className="text-lg font-semibold text-orange-700 mb-3">
                "The Role of Commitment and Hard Work in a Successful Career"
              </h4>
              <p className="text-gray-600 leading-relaxed">
                Learn the fundamental principles of success including commitment,
                hard work, and perseverance. Understand how these values translate
                into career advancement and personal fulfillment.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Program Gallery Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block bg-blue-100 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
              Program Highlights
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              See Our Programs <span className="text-blue-600">In Action</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Explore moments from our various programs and witness the transformation
              happening in the lives of young people across Nigeria.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300">
              <img
                src="/tabio-cms-images/Workshops/WhatsApp Image 2023-08-21 at 15.36.07.jpg"
                alt="Skills Workshop"
                className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent">
                <div className="absolute bottom-6 left-6 right-6">
                  <h3 className="text-white text-xl font-bold mb-2">Skills Workshop</h3>
                  <p className="text-gray-200 text-sm">Hands-on training in various technical skills</p>
                </div>
              </div>
            </div>

            <div className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300">
              <img
                src="/tabio-cms-images/cty-career-sessions/IMG_20230828_111850.jpg"
                alt="Career Session"
                className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent">
                <div className="absolute bottom-6 left-6 right-6">
                  <h3 className="text-white text-xl font-bold mb-2">Career Development</h3>
                  <p className="text-gray-200 text-sm">Professional guidance and mentorship sessions</p>
                </div>
              </div>
            </div>

            <div className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300">
              <img
                src="/tabio-cms-images/cty-golive/0Q1A1788.JPG"
                alt="Catch Them Young"
                className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent">
                <div className="absolute bottom-6 left-6 right-6">
                  <h3 className="text-white text-xl font-bold mb-2">Catch Them Young</h3>
                  <p className="text-gray-200 text-sm">Our flagship youth development program</p>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
              <i className="fas fa-images mr-2"></i>
              View Full Gallery
            </button>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to Make a <span className="text-blue-300">Difference?</span>
          </h2>
          <p className="text-xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Join us in our mission to transform lives through education, skills development,
            and community empowerment. Together, we can build a brighter future for Nigerian youth.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <a
              href="https://forms.gle/Nxon7HurzpFbtk9y5"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-blue-600 hover:bg-blue-50 font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg inline-flex items-center justify-center"
            >
              <i className="fas fa-hand-holding-heart mr-2"></i>
              Volunteer With Us
            </a>
            <button className="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105">
              <i className="fas fa-heart mr-2"></i>
              Support Our Programs
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Programs;
