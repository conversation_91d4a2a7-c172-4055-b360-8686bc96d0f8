import { Link } from 'react-router-dom';

export default function Footer() {
  return (
    <footer className="relative bg-gradient-to-br from-gray-800 via-gray-900 to-black text-white">
      {/* Decorative Wave */}
      <div className="absolute top-0 left-0 right-0 w-full pointer-events-none overflow-hidden -mt-1">
        <svg
          className="relative block w-full h-20"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="none"
          viewBox="0 0 1200 120"
        >
          <path
            d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"
            className="fill-current text-white"
          ></path>
        </svg>
      </div>

      <div className="container mx-auto px-4 pt-20 pb-8">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">

          {/* Organization Info */}
          <div className="lg:col-span-2">
            <div className="mb-6">
              <h3 className="text-3xl font-bold text-white mb-4">
                <span className="text-green-400">TABIO</span> NGO
              </h3>
              <p className="text-gray-300 leading-relaxed mb-6">
                The Assurance Brothers International Outreach (TABIO) is dedicated to transforming lives
                through education, skills development, and community empowerment. Join us in building
                a brighter future for Nigerian youth.
              </p>
            </div>

            {/* Contact Information */}
            <div className="space-y-3 mb-6">
              <div className="flex items-center">
                <i className="fas fa-envelope text-green-400 mr-3"></i>
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center">
                <i className="fas fa-phone text-green-400 mr-3"></i>
                <a href="tel:+2348123456789" className="text-gray-300 hover:text-white transition-colors">
                  +234 ************
                </a>
              </div>
              <div className="flex items-center">
                <i className="fas fa-map-marker-alt text-green-400 mr-3"></i>
                <span className="text-gray-300">Lagos, Nigeria</span>
              </div>
            </div>

            {/* Social Media */}
            <div>
              <h4 className="text-lg font-semibold text-white mb-4">Follow Our Journey</h4>
              <div className="flex space-x-3">
                <a
                  href="https://www.linkedin.com/in/tabio-ngo/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-blue-700 hover:bg-blue-600 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110"
                >
                  <i className="fab fa-linkedin-in text-white"></i>
                </a>
                <a
                  href="https://www.youtube.com/channel/UCkCv1iHeTgMwHuWbxqOE_tQ"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-red-600 hover:bg-red-500 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110"
                >
                  <i className="fab fa-youtube text-white"></i>
                </a>
                <a
                  href="https://twitter.com/TabioNgo01?t=74f-U_M88jzSneS16-66gw&s=08"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-blue-400 hover:bg-blue-300 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110"
                >
                  <i className="fab fa-twitter text-white"></i>
                </a>
                <a
                  href="https://www.instagram.com/tabio_ngo/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-400 hover:to-purple-500 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110"
                >
                  <i className="fab fa-instagram text-white"></i>
                </a>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-6">Quick Links</h4>
            <ul className="space-y-3">
              <li>
                <Link
                  to="/"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300 flex items-center"
                >
                  <i className="fas fa-chevron-right text-green-400 mr-2 text-xs"></i>
                  Home
                </Link>
              </li>
              <li>
                <Link
                  to="/about"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300 flex items-center"
                >
                  <i className="fas fa-chevron-right text-green-400 mr-2 text-xs"></i>
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  to="/programs"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300 flex items-center"
                >
                  <i className="fas fa-chevron-right text-green-400 mr-2 text-xs"></i>
                  Our Programs
                </Link>
              </li>
              <li>
                <Link
                  to="/contact"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300 flex items-center"
                >
                  <i className="fas fa-chevron-right text-green-400 mr-2 text-xs"></i>
                  Contact Us
                </Link>
              </li>
              <li>
                <Link
                  to="/donate"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300 flex items-center"
                >
                  <i className="fas fa-chevron-right text-green-400 mr-2 text-xs"></i>
                  Donate
                </Link>
              </li>
            </ul>
          </div>

          {/* Get Involved */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-6">Get Involved</h4>
            <ul className="space-y-3">
              <li>
                <a
                  href="https://forms.gle/Nxon7HurzpFbtk9y5"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300 flex items-center"
                >
                  <i className="fas fa-chevron-right text-green-400 mr-2 text-xs"></i>
                  Volunteer With Us
                </a>
              </li>
              <li>
                <Link
                  to="/donate"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300 flex items-center"
                >
                  <i className="fas fa-chevron-right text-green-400 mr-2 text-xs"></i>
                  Make a Donation
                </Link>
              </li>
              <li>
                <Link
                  to="/programs"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300 flex items-center"
                >
                  <i className="fas fa-chevron-right text-green-400 mr-2 text-xs"></i>
                  Sponsor a Program
                </Link>
              </li>
              <li>
                <Link
                  to="/contact"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300 flex items-center"
                >
                  <i className="fas fa-chevron-right text-green-400 mr-2 text-xs"></i>
                  Partner With Us
                </Link>
              </li>
            </ul>

            {/* Newsletter Signup */}
            <div className="mt-8">
              <h5 className="text-md font-semibold text-white mb-3">Stay Updated</h5>
              <div className="flex">
                <input
                  type="email"
                  placeholder="Your email"
                  className="flex-1 px-3 py-2 bg-gray-700 text-white rounded-l-lg border border-gray-600 focus:outline-none focus:border-green-400"
                />
                <button className="bg-green-600 hover:bg-green-500 px-4 py-2 rounded-r-lg transition-colors duration-300">
                  <i className="fas fa-paper-plane text-white"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <div className="text-center md:text-left">
              <p className="text-gray-400 text-sm">
                © {new Date().getFullYear()} The Assurance Brothers International Outreach (TABIO). All rights reserved.
              </p>
              <p className="text-gray-500 text-xs mt-1">
                Registered NGO committed to youth development and community empowerment.
              </p>
            </div>

            {/* Developer Credit */}
            <div className="text-center md:text-right">
              <p className="text-gray-400 text-sm mb-1">
                Website built by <span className="text-green-400 font-semibold">Emmanuel Adubi</span>
              </p>
              <div className="text-gray-500 text-xs space-y-1">
                <div className="flex items-center justify-center md:justify-end">
                  <i className="fas fa-phone text-green-400 mr-2"></i>
                  <a href="tel:98163407199" className="hover:text-green-400 transition-colors">
                    98163407199
                  </a>
                </div>
                <div className="flex items-center justify-center md:justify-end">
                  <i className="fas fa-envelope text-green-400 mr-2"></i>
                  <a href="mailto:<EMAIL>" className="hover:text-green-400 transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
