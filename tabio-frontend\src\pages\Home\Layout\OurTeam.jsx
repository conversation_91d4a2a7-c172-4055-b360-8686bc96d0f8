const Team = () => {
    return (
      <div>
        <section className="pt-20 pb-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="container mx-auto px-4">
            <div className="flex flex-wrap justify-center text-center mb-16">
              <div className="w-full lg:w-8/12 px-4">
                <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">TABIO Team Members</h2>
                <p className="text-xl leading-relaxed text-gray-600 max-w-4xl mx-auto">
                  Meet the amazing TABIO team members – devoted Christian men
                  who selflessly dedicate their time to intercede in prayer for
                  families and individuals. They generously give of their
                  resources and time to fulfill the needs of others.
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {/* TABIO President */}
              <div className="group">
                <div className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center border border-gray-100">
                  <div className="relative mb-6">
                    <img
                      alt="TABIO President"
                      src={"/tabio-cms-images/tabio-team-members/demola-adubi.JPG"}
                      className="shadow-lg rounded-full max-w-full mx-auto"
                      style={{ maxWidth: "15rem" }}
                    />
                  </div>
                  <div className="pt-6 text-center">
                    <h5 className="text-xl font-bold">DEMOLA ADUBI</h5>
                    <p className="mt-1 text-sm text-gray-500 uppercase font-semibold">
                      TABIO President
                    </p>
                    <div className="mt-6">
                      <button
                        className="bg-blue-400 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-blue-500 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-twitter"></i>
                      </button>
                      <button
                        className="bg-blue-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-blue-700 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-facebook-f"></i>
                      </button>
                      <button
                        className="bg-pink-500 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-pink-600 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-dribbble"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              {/* Vice President */}
              <div className="group">
                <div className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center border border-gray-100">
                  <div className="relative mb-6">
                    <img
                      alt="TABIO Vice President"
                      src={"/tabio-cms-images/tabio-team-members/RevPatunola.jpg"}
                      className="shadow-lg rounded-full max-w-full mx-auto"
                      style={{ maxWidth: "15rem" }}
                    />
                  </div>
                  <div className="pt-6 text-center">
                    <h5 className="text-xl font-bold">REV'D. PATUNOLA</h5>
                    <p className="mt-1 text-sm text-gray-500 uppercase font-semibold">
                      Vice President
                    </p>
                    <div className="mt-6">
                      <button
                        className="bg-blue-400 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-blue-500 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-twitter"></i>
                      </button>
                      <button
                        className="bg-blue-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-blue-700 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-facebook-f"></i>
                      </button>
                      <button
                        className="bg-pink-500 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-pink-600 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-dribbble"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* General Secretary - Mr. Moses */}
              <div className="group">
                <div className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center border border-gray-100">
                  <div className="relative mb-6">
                    <img
                      alt="General Secretary"
                      src={"/tabio-cms-images/tabio-team-members/MRMOSES.jpg"}
                      className="shadow-lg rounded-full max-w-full mx-auto"
                      style={{ maxWidth: "15rem" }}
                    />
                  </div>
                  <div className="pt-6 text-center">
                    <h5 className="text-xl font-bold">MR. MOSES</h5>
                    <p className="mt-1 text-sm text-gray-500 uppercase font-semibold">
                      General Secretary
                    </p>
                    <div className="mt-6">
                      <button
                        className="bg-green-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-green-700 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-whatsapp"></i>
                      </button>
                      <button
                        className="bg-blue-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-blue-700 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-facebook-f"></i>
                      </button>
                      <button
                        className="bg-gray-800 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-gray-900 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-instagram"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              {/* Treasurer */}
              <div className="group">
                <div className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center border border-gray-100">
                  <div className="relative mb-6">
                    <img
                      alt="Treasurer"
                      src={"/tabio-cms-images/tabio-team-members/person4.png"}
                      className="shadow-lg rounded-full max-w-full mx-auto"
                      style={{ maxWidth: "15rem" }}
                    />
                  </div>
                  <div className="pt-6 text-center">
                    <h5 className="text-xl font-bold">MR. JONATHAN</h5>
                    <p className="mt-1 text-sm text-gray-500 uppercase font-semibold">
                      Treasurer
                    </p>
                    <div className="mt-6">
                      <button
                        className="bg-red-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-red-700 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-google"></i>
                      </button>
                      <button
                        className="bg-blue-400 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-blue-500 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-twitter"></i>
                      </button>
                      <button
                        className="bg-gray-800 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-gray-900 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-instagram"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Emmanuel Adubi */}
              <div className="group">
                <div className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center border border-gray-100">
                  <div className="relative mb-6">
                    <img
                      alt="Emmanuel Adubi"
                      src={"/tabio-cms-images/tabio-team-members/emmanuel-adubi.jpg"}
                      className="shadow-lg rounded-full max-w-full mx-auto"
                      style={{ maxWidth: "15rem" }}
                    />
                  </div>
                  <div className="pt-6 text-center">
                    <h5 className="text-xl font-bold">EMMANUEL ADUBI</h5>
                    <p className="mt-1 text-sm text-gray-500 uppercase font-semibold">
                      Member
                    </p>
                    <div className="mt-6">
                      <button
                        className="bg-pink-500 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-pink-600 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-dribbble"></i>
                      </button>
                      <button
                        className="bg-blue-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-blue-700 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-facebook-f"></i>
                      </button>
                      <button
                        className="bg-blue-400 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-blue-500 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-twitter"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Pastor Shola */}
              <div className="group">
                <div className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center border border-gray-100">
                  <div className="relative mb-6">
                    <img
                      alt="Pastor Shola"
                      src={"/tabio-cms-images/tabio-team-members/person3.png"}
                      className="shadow-lg rounded-full max-w-full mx-auto"
                      style={{ maxWidth: "15rem" }}
                    />
                  </div>
                  <div className="pt-6 text-center">
                    <h5 className="text-xl font-bold">PST. SHOLA</h5>
                    <p className="mt-1 text-sm text-gray-500 uppercase font-semibold">
                      Member
                    </p>
                    <div className="mt-6">
                      <button
                        className="bg-pink-500 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-pink-600 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-dribbble"></i>
                      </button>
                      <button
                        className="bg-red-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-red-700 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-google"></i>
                      </button>
                      <button
                        className="bg-blue-400 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-blue-500 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-twitter"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Mr. Peter */}
              <div className="group">
                <div className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center border border-gray-100">
                  <div className="relative mb-6">
                    <img
                      alt="Mr. Peter"
                      src={"/tabio-cms-images/tabio-team-members/person2.png"}
                      className="shadow-lg rounded-full max-w-full mx-auto"
                      style={{ maxWidth: "15rem" }}
                    />
                  </div>
                  <div className="pt-6 text-center">
                    <h5 className="text-xl font-bold">MR. PETER</h5>
                    <p className="mt-1 text-sm text-gray-500 uppercase font-semibold">
                      Member
                    </p>
                    <div className="mt-6">
                      <button
                        className="bg-pink-500 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-pink-600 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-dribbble"></i>
                      </button>
                      <button
                        className="bg-red-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-red-700 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-google"></i>
                      </button>
                      <button
                        className="bg-blue-400 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-blue-500 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-twitter"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Apostle */}
              <div className="group">
                <div className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center border border-gray-100">
                  <div className="relative mb-6">
                    <img
                      alt="Apostle"
                      src={"/tabio-cms-images/tabio-team-members/apostle1.JPG"}
                      className="shadow-lg rounded-full max-w-full mx-auto"
                      style={{ maxWidth: "15rem" }}
                    />
                  </div>
                  <div className="pt-6 text-center">
                    <h5 className="text-xl font-bold">APOSTLE</h5>
                    <p className="mt-1 text-sm text-gray-500 uppercase font-semibold">
                      Member
                    </p>
                    <div className="mt-6">
                      <button
                        className="bg-purple-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-purple-700 transition-colors"
                        type="button"
                      >
                        <i className="fas fa-cross"></i>
                      </button>
                      <button
                        className="bg-blue-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-blue-700 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-facebook-f"></i>
                      </button>
                      <button
                        className="bg-green-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1 hover:bg-green-700 transition-colors"
                        type="button"
                      >
                        <i className="fab fa-whatsapp"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </section>
      </div>
    );
}

export default Team
