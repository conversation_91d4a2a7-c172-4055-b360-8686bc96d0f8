const Team = () => {
    return (
      <div>
        <section className="pt-20 pb-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="container mx-auto px-4">
            <div className="flex flex-wrap justify-center text-center mb-16">
              <div className="w-full lg:w-8/12 px-4">
                <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">TABIO Team Members</h2>
                <p className="text-xl leading-relaxed text-gray-600 max-w-4xl mx-auto">
                  Meet the amazing TABIO team members – devoted Christian men
                  who selflessly dedicate their time to intercede in prayer for
                  families and individuals. They generously give of their
                  resources and time to fulfill the needs of others.
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="group">
                <div className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center border border-gray-100">
                  <div className="relative mb-6">
                    <img
                      alt="Tabio president"
                      src={"/tabio-cms-images/tabio-team-members/Person6.png"}
                      className="shadow-lg rounded-full max-w-full mx-auto"
                      style={{ maxWidth: "15rem" }}
                    />
                  </div>
                  <div className="pt-6 text-center">
                    <h5 className="text-xl font-bold">DEMOLA' ADUBI</h5>
                    <p className="mt-1 text-sm text-gray-500 uppercase font-semibold">
                      TABIO President
                    </p>
                    <div className="mt-6">
                      <button
                        className="bg-blue-400 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1"
                        type="button"
                      >
                        <i className="fab fa-twitter"></i>
                      </button>
                      <button
                        className="bg-blue-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1"
                        type="button"
                      >
                        <i className="fab fa-facebook-f"></i>
                      </button>
                      <button
                        className="bg-pink-500 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1"
                        type="button"
                      >
                        <i className="fab fa-dribbble"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div className="group">
                <div className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center border border-gray-100">
                  <div className="relative mb-6">
                    <img
                      alt="Tabio Vice President"
                      src={"/tabio-cms-images/tabio-team-members/person5.png"}
                      className="shadow-lg rounded-full max-w-full mx-auto"
                      style={{ maxWidth: "15rem" }}
                    />
                  </div>
                  <div className="pt-6 text-center">
                    <h5 className="text-xl font-bold">REV'D. PATUNOLA</h5>
                    <p className="mt-1 text-sm text-gray-500 uppercase font-semibold">
                      Vice President
                    </p>
                    <div className="mt-6">
                      <button
                        className="bg-blue-400 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1"
                        type="button"
                      >
                        <i className="fab fa-twitter"></i>
                      </button>
                      <button
                        className="bg-blue-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1"
                        type="button"
                      >
                        <i className="fab fa-facebook-f"></i>
                      </button>
                      <button
                        className="bg-pink-500 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1"
                        type="button"
                      >
                        <i className="fab fa-dribbble"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div className="group">
                <div className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center border border-gray-100">
                  <div className="relative mb-6">
                    <img
                      alt="MR. TAIWO"
                      src={"/tabio-cms-images/tabio-team-members/person1.png"}
                      className="shadow-lg rounded-full max-w-full mx-auto"
                      style={{ maxWidth: "15rem" }}
                    />
                  </div>
                  <div className="pt-6 text-center">
                    <h5 className="text-xl font-bold">MR. TAIWO</h5>
                    <p className="mt-1 text-sm text-gray-500 uppercase font-semibold">
                      General Secretary
                    </p>
                    <div className="mt-6">
                      <button
                        className="bg-red-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1"
                        type="button"
                      >
                        <i className="fab fa-google"></i>
                      </button>
                      <button
                        className="bg-blue-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1"
                        type="button"
                      >
                        <i className="fab fa-facebook-f"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div className="group">
                <div className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center border border-gray-100">
                  <div className="relative mb-6">
                    <img
                      alt="MR. JONATHAN"
                      src={"/tabio-cms-images/tabio-team-members/person4.png"}
                      className="shadow-lg rounded-full max-w-full mx-auto"
                      style={{ maxWidth: "15rem" }}
                    />
                  </div>
                  <div className="pt-6 text-center">
                    <h5 className="text-xl font-bold">MR. JONATHAN</h5>
                    <p className="mt-1 text-sm text-gray-500 uppercase font-semibold">
                      Treasurer
                    </p>
                    <div className="mt-6">
                      <button
                        className="bg-red-600 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1"
                        type="button"
                      >
                        <i className="fab fa-google"></i>
                      </button>
                      <button
                        className="bg-blue-400 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1"
                        type="button"
                      >
                        <i className="fab fa-twitter"></i>
                      </button>
                      <button
                        className="bg-gray-800 text-white w-8 h-8 rounded-full outline-none focus:outline-none mr-1 mb-1"
                        type="button"
                      >
                        <i className="fab fa-instagram"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </section>
      </div>
    );
}

export default Team
