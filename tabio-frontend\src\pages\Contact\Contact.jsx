const Contact = () => {
  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-green-600 via-green-700 to-green-800 py-20 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="mb-8">
              <span className="inline-block bg-white bg-opacity-20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium border border-white border-opacity-30">
                Get In Touch
              </span>
            </div>
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              Contact <span className="text-green-300">TABIO</span>
            </h1>
            <p className="text-xl md:text-2xl text-green-100 leading-relaxed max-w-3xl mx-auto">
              Ready to join our mission or need more information? We'd love to hear from you.
              Reach out and let's make a difference together.
            </p>

            <div className="mt-12">
              <div className="animate-bounce">
                <i className="fas fa-chevron-down text-white text-2xl"></i>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Information & Form Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Contact Information */}
            <div className="order-2 lg:order-1">
              <div className="mb-8">
                <span className="inline-block bg-green-100 text-green-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
                  Contact Information
                </span>
                <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6 leading-tight">
                  Let's Start a <span className="text-green-600">Conversation</span>
                </h2>
                <p className="text-lg text-gray-600 leading-relaxed mb-8">
                  Whether you're interested in volunteering, partnering with us, or learning more about our programs,
                  we're here to help. Choose the best way to reach us below.
                </p>
              </div>

              {/* Contact Cards */}
              <div className="space-y-6">
                {/* Email Contact */}
                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-2xl p-6 border border-green-200 hover:shadow-lg transition-all duration-300">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center">
                        <i className="fas fa-envelope text-white text-xl"></i>
                      </div>
                    </div>
                    <div className="ml-4 flex-1">
                      <h3 className="text-xl font-bold text-gray-800 mb-2">Email Us</h3>
                      <p className="text-gray-600 mb-3">Send us a message and we'll respond within 24 hours</p>
                      <a href="mailto:<EMAIL>" className="text-green-600 font-semibold hover:text-green-700 transition-colors">
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                </div>

                {/* Phone Contact */}
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200 hover:shadow-lg transition-all duration-300">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
                        <i className="fas fa-phone text-white text-xl"></i>
                      </div>
                    </div>
                    <div className="ml-4 flex-1">
                      <h3 className="text-xl font-bold text-gray-800 mb-2">Call Us</h3>
                      <p className="text-gray-600 mb-3">Speak directly with our team</p>
                      <a href="tel:+2348123456789" className="text-blue-600 font-semibold hover:text-blue-700 transition-colors">
                        +234 ************
                      </a>
                    </div>
                  </div>
                </div>

                {/* Office Location */}
                <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200 hover:shadow-lg transition-all duration-300">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center">
                        <i className="fas fa-map-marker-alt text-white text-xl"></i>
                      </div>
                    </div>
                    <div className="ml-4 flex-1">
                      <h3 className="text-xl font-bold text-gray-800 mb-2">Visit Us</h3>
                      <p className="text-gray-600 mb-3">Our office location in Lagos</p>
                      <address className="text-purple-600 font-semibold not-italic">
                        Lagos, Nigeria<br />
                        <span className="text-gray-600 font-normal">By appointment only</span>
                      </address>
                    </div>
                  </div>
                </div>

                {/* Volunteer Link */}
                <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200 hover:shadow-lg transition-all duration-300">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-orange-600 rounded-xl flex items-center justify-center">
                        <i className="fas fa-hand-holding-heart text-white text-xl"></i>
                      </div>
                    </div>
                    <div className="ml-4 flex-1">
                      <h3 className="text-xl font-bold text-gray-800 mb-2">Volunteer With Us</h3>
                      <p className="text-gray-600 mb-3">Join our mission to transform lives</p>
                      <a
                        href="https://forms.gle/Nxon7HurzpFbtk9y5"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-orange-600 font-semibold hover:text-orange-700 transition-colors"
                      >
                        Apply to Volunteer →
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="order-1 lg:order-2">
              <div className="bg-gradient-to-br from-gray-50 to-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100">
                <div className="mb-8">
                  <h3 className="text-3xl font-bold text-gray-800 mb-4">Send us a Message</h3>
                  <p className="text-gray-600">
                    Fill out the form below and we'll get back to you within 24 hours.
                  </p>
                </div>

                <form className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        First Name *
                      </label>
                      <input
                        type="text"
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                        placeholder="Your first name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                        placeholder="Your last name"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                      placeholder="+234 xxx xxx xxxx"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Subject *
                    </label>
                    <select
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                    >
                      <option value="">Select a subject</option>
                      <option value="volunteer">Volunteer Opportunities</option>
                      <option value="partnership">Partnership Inquiry</option>
                      <option value="donation">Donation Information</option>
                      <option value="programs">Program Information</option>
                      <option value="general">General Inquiry</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Message *
                    </label>
                    <textarea
                      required
                      rows="5"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 resize-none"
                      placeholder="Tell us how we can help you or how you'd like to get involved..."
                    ></textarea>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white font-semibold py-4 px-8 rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    <i className="fas fa-paper-plane mr-2"></i>
                    Send Message
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block bg-green-100 text-green-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
              Frequently Asked Questions
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Common <span className="text-green-600">Questions</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Find quick answers to the most common questions about TABIO, our programs, and how to get involved.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-6">
              {/* FAQ Item 1 */}
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-question-circle text-green-600 mr-3"></i>
                  How can I volunteer with TABIO?
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  You can apply to volunteer by filling out our online application form. We welcome volunteers
                  for various programs including Catch Them Young, educational support, and community outreach.
                  Visit our volunteer application link or contact us directly for more information.
                </p>
              </div>

              {/* FAQ Item 2 */}
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-question-circle text-green-600 mr-3"></i>
                  What programs does TABIO offer?
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  TABIO offers comprehensive programs including our flagship "Catch Them Young" skills development program,
                  education support (SSCE sponsorship and tertiary education financing), women empowerment through SME financing,
                  career development sessions, orphanage support, and spiritual development through prayer retreats.
                </p>
              </div>

              {/* FAQ Item 3 */}
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-question-circle text-green-600 mr-3"></i>
                  How can I apply for educational support?
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Educational support applications are typically processed through our community outreach programs.
                  Contact us directly with your educational needs, and our team will guide you through the application process.
                  We support SSCE exam fees and tertiary institution tuition based on available resources and eligibility criteria.
                </p>
              </div>

              {/* FAQ Item 4 */}
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-question-circle text-green-600 mr-3"></i>
                  Can organizations partner with TABIO?
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Yes! We welcome partnerships with organizations that share our vision of youth development and community empowerment.
                  Whether you're a corporate entity, NGO, or educational institution, we'd love to explore collaboration opportunities.
                  Please contact us with your partnership proposal.
                </p>
              </div>

              {/* FAQ Item 5 */}
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-question-circle text-green-600 mr-3"></i>
                  How can I donate to support TABIO's programs?
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  We appreciate your interest in supporting our mission! You can make donations through our secure donation channels.
                  Contact us directly for donation information, or visit our donation page for more details on how your contribution
                  can help transform lives in Nigerian communities.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Social Media & Connect Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block bg-blue-100 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
              Stay Connected
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Follow Our <span className="text-blue-600">Journey</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Stay updated with our latest programs, success stories, and community impact through our social media channels.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {/* LinkedIn */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 text-center hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-16 h-16 bg-blue-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fab fa-linkedin-in text-white text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">LinkedIn</h3>
              <p className="text-gray-600 mb-6">Connect with us professionally</p>
              <a
                href="https://www.linkedin.com/in/tabio-ngo/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 font-semibold hover:text-blue-700 transition-colors"
              >
                Connect →
              </a>
            </div>

            {/* YouTube */}
            <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-8 text-center hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fab fa-youtube text-white text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">YouTube</h3>
              <p className="text-gray-600 mb-6">Watch our program videos and testimonials</p>
              <a
                href="https://www.youtube.com/channel/UCkCv1iHeTgMwHuWbxqOE_tQ"
                target="_blank"
                rel="noopener noreferrer"
                className="text-red-600 font-semibold hover:text-red-700 transition-colors"
              >
                Subscribe →
              </a>
            </div>

            {/* Twitter */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 text-center hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-16 h-16 bg-blue-400 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fab fa-twitter text-white text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">Twitter</h3>
              <p className="text-gray-600 mb-6">Get real-time updates and announcements</p>
              <a
                href="https://twitter.com/TabioNgo01?t=74f-U_M88jzSneS16-66gw&s=08"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 font-semibold hover:text-blue-700 transition-colors"
              >
                Follow Us →
              </a>
            </div>

            {/* Facebook */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 text-center hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fab fa-facebook-f text-white text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">Facebook</h3>
              <p className="text-gray-600 mb-6">Follow our updates and community stories</p>
              <a href="#" className="text-blue-600 font-semibold hover:text-blue-700 transition-colors">
                Follow Us →
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-r from-green-600 to-green-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to Make an <span className="text-green-300">Impact?</span>
          </h2>
          <p className="text-xl text-green-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Whether you want to volunteer, partner with us, or learn more about our programs,
            we're here to help you get involved in transforming lives across Nigeria.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <a
              href="https://forms.gle/Nxon7HurzpFbtk9y5"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-green-600 hover:bg-green-50 font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg inline-flex items-center justify-center"
            >
              <i className="fas fa-hand-holding-heart mr-2"></i>
              Start Volunteering
            </a>
            <button className="border-2 border-white text-white hover:bg-white hover:text-green-600 font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105">
              <i className="fas fa-envelope mr-2"></i>
              Send Us a Message
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
