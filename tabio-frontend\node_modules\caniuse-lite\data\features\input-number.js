module.exports={A:{A:{"2":"J E F G NC","129":"A B"},B:{"1":"0 1 2 3 P Q R S T U V W X Y Z a b c d e f g h i j k l q r s t u v w x y z D","129":"C K","1025":"L H M N O"},C:{"2":"4 5 6 7 8 9 OC 2B I J E F G A B C K L H M N O m n o AB BB PC QC","513":"0 1 2 3 CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB 3B gB 4B hB iB jB kB lB mB nB oB pB qB rB p sB tB uB vB wB P Q R 5B S T U V W X Y Z a b c d e f g h i j k l q r s t u v w x y z D 6B 7B 8B"},D:{"1":"0 1 2 3 5 6 7 8 9 J E F G A B C K L H M N O m n o AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB 3B gB 4B hB iB jB kB lB mB nB oB pB qB rB p sB tB uB vB wB P Q R S T U V W X Y Z a b c d e f g h i j k l q r s t u v w x y z D 6B 7B 8B","2":"4 I"},E:{"1":"4 J E F G A B C K L H SC TC UC VC AC xB yB BC WC XC CC DC zB YC 0B EC FC GC HC IC JC 1B KC ZC","2":"I RC 9B"},F:{"1":"5 6 7 8 9 G B C H M N O m n o AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB p sB tB uB vB wB P Q R 5B S T U V W X Y Z a b c d e f g h i j k l aC bC cC dC xB LC eC yB"},G:{"388":"F 9B fC MC gC hC iC jC kC lC mC nC oC pC qC rC sC tC uC vC wC xC yC CC DC zB zC 0B EC FC GC HC IC JC 1B KC"},H:{"2":"0C"},I:{"2":"2B 1C 2C 3C","388":"I D 4C MC 5C 6C"},J:{"2":"E","388":"A"},K:{"1":"A B C xB LC yB","388":"p"},L:{"388":"D"},M:{"641":"D"},N:{"388":"A B"},O:{"388":"zB"},P:{"388":"I m n o 7C 8C 9C AD BD AC CD DD ED FD GD 0B 1B HD ID"},Q:{"388":"BC"},R:{"388":"JD"},S:{"513":"KD LD"}},B:1,C:"Number input type",D:true};
