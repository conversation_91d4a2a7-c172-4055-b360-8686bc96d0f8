import {createBrowserRouter } from 'react-router-dom';
import App from './App';
import Home from './pages/Home/Home';
import About from './pages/About/About';
import Contact from './pages/Contact/Contact';
import Donate from './pages/Donate/Donate';
import Programs from './pages/Programs/Programs';


const routes = createBrowserRouter([
    {
        path:'/',
        element: <App />,
        children:[
            {
                path:'/',
                element: <Home/>
            },
            {
                path:'/about',
                element: <About/>
            },
            {
                path:'/contact',
                element: <Contact/>
            },
            {
                path:'/donate',
                element: <Donate/>
            },
            {
                path:'/programs',
                element: <Programs/>
            },
        ]
    },
])

export default routes;