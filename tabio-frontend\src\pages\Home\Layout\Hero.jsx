const Hero = () => {
    return (
      <div>
        <div
          className="relative pt-16 pb-32 flex content-center items-center justify-center"
          style={{
            minHeight: "75vh",
          }}
        >
          <div
            className="absolute top-0 w-full h-full bg-center bg-cover"
            style={{
              backgroundImage:
                "url('/tabio-cms-images/cty-golive/0Q1A1762.JPG')",
            }}
          >
            <span
              id="blackOverlay"
              className="w-full h-full absolute opacity-75 bg-black"
            ></span>
          </div>
          <div className="container relative mx-auto">
            <div className="items-center flex flex-wrap">
              <div className="w-full lg:w-6/12 flex align-items-center justify-content-center px-4 mx-auto text-center">
                <div className=" mx-auto">
                  <h1 className="text-white font-semibold text-3xl md:text-4xl lg:text-5xl">
                    THE ASSURANCE BROTHERS' INTERNATIONAL OUTREACH (TABIO)
                  </h1>
                  <p className="mt-4 text-lg text-gray-300">
                    TABIO | interceding for individuals, societies, and nations of Africa, particularly
                    the Christian community. 
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div
            className="top-auto bottom-0 left-0 right-0 w-full absolute pointer-events-none overflow-hidden"
            style={{ height: "70px" }}
          >
            <svg
              className="absolute bottom-0 overflow-hidden"
              xmlns="http://www.w3.org/2000/svg"
              preserveAspectRatio="none"
              version="1.1"
              viewBox="0 0 2560 100"
              x="0"
              y="0"
            >
              <polygon
                className="text-gray-300 fill-current"
                points="2560 0 2560 100 0 100"
              ></polygon>
            </svg>
          </div>
        </div>
      </div>
    );
}

export default Hero
