import { useState, useEffect } from 'react';

const Hero = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Hero images carousel
  const heroImages = [
    '/tabio-cms-images/cty-golive/0Q1A1762.JPG',
    '/tabio-cms-images/cty-golive/0Q1A1788.JPG',
    '/tabio-cms-images/cty-golive/0Q1A1843.JPG',
    '/tabio-cms-images/cty-golive/0Q1A1841.JPG',
    '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-21 at 15.36.07.jpg',
    '/tabio-cms-images/cty-career-sessions/IMG_20230828_111850.jpg'
  ];

  // Auto-advance carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        (prevIndex + 1) % heroImages.length
      );
    }, 5000); // Change image every 5 seconds

    return () => clearInterval(interval);
  }, [heroImages.length]);

  const goToSlide = (index) => {
    setCurrentImageIndex(index);
  };

  return (
    <div>
      <div
        className="relative pt-16 pb-32 flex content-center items-center justify-center overflow-hidden"
        style={{
          minHeight: "85vh",
        }}
      >
        {/* Background Images Carousel */}
        {heroImages.map((image, index) => (
          <div
            key={index}
            className={`absolute top-0 w-full h-full bg-center bg-cover transition-opacity duration-1000 ${
              index === currentImageIndex ? 'opacity-100' : 'opacity-0'
            }`}
            style={{
              backgroundImage: `url('${image}')`,
            }}
          >
            <span className="w-full h-full absolute bg-gradient-to-r from-black/70 via-black/50 to-black/70"></span>
          </div>
        ))}

        {/* Content Container */}
        <div className="container relative mx-auto z-10">
            <div className="items-center flex flex-wrap">
              <div className="w-full lg:w-8/12 flex align-items-center justify-content-center px-4 mx-auto text-center">
                <div className="mx-auto">
                  <div className="mb-6">
                    <span className="inline-block bg-green-600/20 backdrop-blur-sm text-green-300 px-4 py-2 rounded-full text-sm font-medium border border-green-400/30">
                      Transforming Lives Since 2004
                    </span>
                  </div>
                  <h1 className="text-white font-bold text-4xl md:text-5xl lg:text-6xl xl:text-7xl leading-tight mb-6">
                    THE ASSURANCE BROTHERS'
                    <span className="block text-green-400">INTERNATIONAL OUTREACH</span>
                    <span className="block text-2xl md:text-3xl lg:text-4xl font-medium text-gray-300 mt-2">(TABIO)</span>
                  </h1>
                  <p className="mt-6 text-xl md:text-2xl text-gray-200 leading-relaxed max-w-4xl mx-auto">
                    Interceding for individuals, societies, and nations of Africa, particularly
                    the Christian community through education, mentorship, and community development.
                  </p>

                  {/* Call to Action Buttons */}
                  <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center">
                    <button className="bg-green-600 hover:bg-green-700 text-white font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg">
                      Join Our Mission
                    </button>
                    <button className="border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105">
                      Learn More
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Carousel Indicators */}
          <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-10">
            <div className="flex space-x-3">
              {heroImages.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentImageIndex
                      ? 'bg-white scale-125'
                      : 'bg-white/50 hover:bg-white/75'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </div>
          <div
            className="top-auto bottom-0 left-0 right-0 w-full absolute pointer-events-none overflow-hidden"
            style={{ height: "70px" }}
          >
            <svg
              className="absolute bottom-0 overflow-hidden"
              xmlns="http://www.w3.org/2000/svg"
              preserveAspectRatio="none"
              version="1.1"
              viewBox="0 0 2560 100"
              x="0"
              y="0"
            >
              <polygon
                className="text-gray-300 fill-current"
                points="2560 0 2560 100 0 100"
              ></polygon>
            </svg>
          </div>
        </div>
      </div>
    );
}

export default Hero
