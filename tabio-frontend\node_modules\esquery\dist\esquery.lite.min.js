!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("estraverse")):"function"==typeof define&&define.amd?define(["estraverse"],e):(t=t||self).esquery=e(t.estraverse)}(this,(function(t){"use strict";function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,u,o,a,s=[],c=!0,i=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){i=!0,u=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(i)throw u}}return s}}(t,e)||u(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(t){return function(t){if(Array.isArray(t))return o(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||u(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,e){if(t){if("string"==typeof t)return o(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(t,e):void 0}}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}t=t&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t;"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;var a=function(t,e){return t(e={exports:{}},e.exports),e.exports}((function(t){t.exports&&(t.exports=function(){function t(e,r,n,u){this.message=e,this.expected=r,this.found=n,this.location=u,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,t)}return function(t,e){function r(){this.constructor=t}r.prototype=e.prototype,t.prototype=new r}(t,Error),t.buildMessage=function(t,e){var r={literal:function(t){return'"'+u(t.text)+'"'},class:function(t){var e,r="";for(e=0;e<t.parts.length;e++)r+=t.parts[e]instanceof Array?o(t.parts[e][0])+"-"+o(t.parts[e][1]):o(t.parts[e]);return"["+(t.inverted?"^":"")+r+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function u(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function o(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}return"Expected "+function(t){var e,n,u,o=new Array(t.length);for(e=0;e<t.length;e++)o[e]=(u=t[e],r[u.type](u));if(o.sort(),o.length>0){for(e=1,n=1;e<o.length;e++)o[e-1]!==o[e]&&(o[n]=o[e],n++);o.length=n}switch(o.length){case 1:return o[0];case 2:return o[0]+" or "+o[1];default:return o.slice(0,-1).join(", ")+", or "+o[o.length-1]}}(t)+" but "+function(t){return t?'"'+u(t)+'"':"end of input"}(e)+" found."},{SyntaxError:t,parse:function(e,r){r=void 0!==r?r:{};var n,u,o,a,s={},c={start:yt},i=yt,l=lt(" ",!1),f=/^[^ [\],():#!=><~+.]/,h=ft([" ","[","]",",","(",")",":","#","!","=",">","<","~","+","."],!0,!1),p=lt(">",!1),v=lt("~",!1),y=lt("+",!1),d=lt(",",!1),A=lt("!",!1),x=lt("*",!1),g=lt("#",!1),m=lt("[",!1),b=lt("]",!1),P=/^[><!]/,C=ft([">","<","!"],!1,!1),w=lt("=",!1),j=function(t){return(t||"")+"="},E=/^[><]/,S=ft([">","<"],!1,!1),k=lt(".",!1),I=function(t,e,r){return{type:"attribute",name:t,operator:e,value:r}},T=lt('"',!1),F=/^[^\\"]/,K=ft(["\\",'"'],!0,!1),O=lt("\\",!1),D={type:"any"},L=function(t,e){return t+e},R=function(t){return{type:"literal",value:(e=t.join(""),e.replace(/\\(.)/g,(function(t,e){switch(e){case"b":return"\b";case"f":return"\f";case"n":return"\n";case"r":return"\r";case"t":return"\t";case"v":return"\v";default:return e}})))};var e},M=lt("'",!1),U=/^[^\\']/,_=ft(["\\","'"],!0,!1),q=/^[0-9]/,G=ft([["0","9"]],!1,!1),H=lt("type(",!1),V=/^[^ )]/,W=ft([" ",")"],!0,!1),$=lt(")",!1),z=/^[imsu]/,B=ft(["i","m","s","u"],!1,!1),J=lt("/",!1),N=/^[^\/]/,Q=ft(["/"],!0,!1),X=lt(":not(",!1),Y=lt(":matches(",!1),Z=lt(":has(",!1),tt=lt(":first-child",!1),et=lt(":last-child",!1),rt=lt(":nth-child(",!1),nt=lt(":nth-last-child(",!1),ut=lt(":",!1),ot=0,at=[{line:1,column:1}],st=0,ct=[],it={};if("startRule"in r){if(!(r.startRule in c))throw new Error("Can't start parsing from rule \""+r.startRule+'".');i=c[r.startRule]}function lt(t,e){return{type:"literal",text:t,ignoreCase:e}}function ft(t,e,r){return{type:"class",parts:t,inverted:e,ignoreCase:r}}function ht(t){var r,n=at[t];if(n)return n;for(r=t-1;!at[r];)r--;for(n={line:(n=at[r]).line,column:n.column};r<t;)10===e.charCodeAt(r)?(n.line++,n.column=1):n.column++,r++;return at[t]=n,n}function pt(t,e){var r=ht(t),n=ht(e);return{start:{offset:t,line:r.line,column:r.column},end:{offset:e,line:n.line,column:n.column}}}function vt(t){ot<st||(ot>st&&(st=ot,ct=[]),ct.push(t))}function yt(){var t,e,r,n,u=30*ot+0,o=it[u];return o?(ot=o.nextPos,o.result):(t=ot,(e=dt())!==s&&(r=gt())!==s&&dt()!==s?t=e=1===(n=r).length?n[0]:{type:"matches",selectors:n}:(ot=t,t=s),t===s&&(t=ot,(e=dt())!==s&&(e=void 0),t=e),it[u]={nextPos:ot,result:t},t)}function dt(){var t,r,n=30*ot+1,u=it[n];if(u)return ot=u.nextPos,u.result;for(t=[],32===e.charCodeAt(ot)?(r=" ",ot++):(r=s,vt(l));r!==s;)t.push(r),32===e.charCodeAt(ot)?(r=" ",ot++):(r=s,vt(l));return it[n]={nextPos:ot,result:t},t}function At(){var t,r,n,u=30*ot+2,o=it[u];if(o)return ot=o.nextPos,o.result;if(r=[],f.test(e.charAt(ot))?(n=e.charAt(ot),ot++):(n=s,vt(h)),n!==s)for(;n!==s;)r.push(n),f.test(e.charAt(ot))?(n=e.charAt(ot),ot++):(n=s,vt(h));else r=s;return r!==s&&(r=r.join("")),t=r,it[u]={nextPos:ot,result:t},t}function xt(){var t,r,n,u=30*ot+3,o=it[u];return o?(ot=o.nextPos,o.result):(t=ot,(r=dt())!==s?(62===e.charCodeAt(ot)?(n=">",ot++):(n=s,vt(p)),n!==s&&dt()!==s?t=r="child":(ot=t,t=s)):(ot=t,t=s),t===s&&(t=ot,(r=dt())!==s?(126===e.charCodeAt(ot)?(n="~",ot++):(n=s,vt(v)),n!==s&&dt()!==s?t=r="sibling":(ot=t,t=s)):(ot=t,t=s),t===s&&(t=ot,(r=dt())!==s?(43===e.charCodeAt(ot)?(n="+",ot++):(n=s,vt(y)),n!==s&&dt()!==s?t=r="adjacent":(ot=t,t=s)):(ot=t,t=s),t===s&&(t=ot,32===e.charCodeAt(ot)?(r=" ",ot++):(r=s,vt(l)),r!==s&&(n=dt())!==s?t=r="descendant":(ot=t,t=s)))),it[u]={nextPos:ot,result:t},t)}function gt(){var t,r,n,u,o,a,c,i,l=30*ot+4,f=it[l];if(f)return ot=f.nextPos,f.result;if(t=ot,(r=mt())!==s){for(n=[],u=ot,(o=dt())!==s?(44===e.charCodeAt(ot)?(a=",",ot++):(a=s,vt(d)),a!==s&&(c=dt())!==s&&(i=mt())!==s?u=o=[o,a,c,i]:(ot=u,u=s)):(ot=u,u=s);u!==s;)n.push(u),u=ot,(o=dt())!==s?(44===e.charCodeAt(ot)?(a=",",ot++):(a=s,vt(d)),a!==s&&(c=dt())!==s&&(i=mt())!==s?u=o=[o,a,c,i]:(ot=u,u=s)):(ot=u,u=s);n!==s?t=r=[r].concat(n.map((function(t){return t[3]}))):(ot=t,t=s)}else ot=t,t=s;return it[l]={nextPos:ot,result:t},t}function mt(){var t,e,r,n,u,o,a,c=30*ot+5,i=it[c];if(i)return ot=i.nextPos,i.result;if(t=ot,(e=bt())!==s){for(r=[],n=ot,(u=xt())!==s&&(o=bt())!==s?n=u=[u,o]:(ot=n,n=s);n!==s;)r.push(n),n=ot,(u=xt())!==s&&(o=bt())!==s?n=u=[u,o]:(ot=n,n=s);r!==s?(a=e,t=e=r.reduce((function(t,e){return{type:e[0],left:t,right:e[1]}}),a)):(ot=t,t=s)}else ot=t,t=s;return it[c]={nextPos:ot,result:t},t}function bt(){var t,r,n,u,o,a,c,i=30*ot+6,l=it[i];if(l)return ot=l.nextPos,l.result;if(t=ot,33===e.charCodeAt(ot)?(r="!",ot++):(r=s,vt(A)),r===s&&(r=null),r!==s){if(n=[],(u=Pt())!==s)for(;u!==s;)n.push(u),u=Pt();else n=s;n!==s?(o=r,c=1===(a=n).length?a[0]:{type:"compound",selectors:a},o&&(c.subject=!0),t=r=c):(ot=t,t=s)}else ot=t,t=s;return it[i]={nextPos:ot,result:t},t}function Pt(){var t,r=30*ot+7,n=it[r];return n?(ot=n.nextPos,n.result):((t=function(){var t,r,n=30*ot+8,u=it[n];return u?(ot=u.nextPos,u.result):(42===e.charCodeAt(ot)?(r="*",ot++):(r=s,vt(x)),r!==s&&(r={type:"wildcard",value:r}),t=r,it[n]={nextPos:ot,result:t},t)}())===s&&(t=function(){var t,r,n,u=30*ot+9,o=it[u];return o?(ot=o.nextPos,o.result):(t=ot,35===e.charCodeAt(ot)?(r="#",ot++):(r=s,vt(g)),r===s&&(r=null),r!==s&&(n=At())!==s?t=r={type:"identifier",value:n}:(ot=t,t=s),it[u]={nextPos:ot,result:t},t)}())===s&&(t=function(){var t,r,n,u,o=30*ot+10,a=it[o];return a?(ot=a.nextPos,a.result):(t=ot,91===e.charCodeAt(ot)?(r="[",ot++):(r=s,vt(m)),r!==s&&dt()!==s&&(n=function(){var t,r,n,u,o=30*ot+14,a=it[o];return a?(ot=a.nextPos,a.result):(t=ot,(r=Ct())!==s&&dt()!==s&&(n=function(){var t,r,n,u=30*ot+12,o=it[u];return o?(ot=o.nextPos,o.result):(t=ot,33===e.charCodeAt(ot)?(r="!",ot++):(r=s,vt(A)),r===s&&(r=null),r!==s?(61===e.charCodeAt(ot)?(n="=",ot++):(n=s,vt(w)),n!==s?(r=j(r),t=r):(ot=t,t=s)):(ot=t,t=s),it[u]={nextPos:ot,result:t},t)}())!==s&&dt()!==s?((u=function(){var t,r,n,u,o,a=30*ot+18,c=it[a];if(c)return ot=c.nextPos,c.result;if(t=ot,"type("===e.substr(ot,5)?(r="type(",ot+=5):(r=s,vt(H)),r!==s)if(dt()!==s){if(n=[],V.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(W)),u!==s)for(;u!==s;)n.push(u),V.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(W));else n=s;n!==s&&(u=dt())!==s?(41===e.charCodeAt(ot)?(o=")",ot++):(o=s,vt($)),o!==s?(r={type:"type",value:n.join("")},t=r):(ot=t,t=s)):(ot=t,t=s)}else ot=t,t=s;else ot=t,t=s;return it[a]={nextPos:ot,result:t},t}())===s&&(u=function(){var t,r,n,u,o,a,c=30*ot+20,i=it[c];if(i)return ot=i.nextPos,i.result;if(t=ot,47===e.charCodeAt(ot)?(r="/",ot++):(r=s,vt(J)),r!==s){if(n=[],N.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(Q)),u!==s)for(;u!==s;)n.push(u),N.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(Q));else n=s;n!==s?(47===e.charCodeAt(ot)?(u="/",ot++):(u=s,vt(J)),u!==s?((o=function(){var t,r,n=30*ot+19,u=it[n];if(u)return ot=u.nextPos,u.result;if(t=[],z.test(e.charAt(ot))?(r=e.charAt(ot),ot++):(r=s,vt(B)),r!==s)for(;r!==s;)t.push(r),z.test(e.charAt(ot))?(r=e.charAt(ot),ot++):(r=s,vt(B));else t=s;return it[n]={nextPos:ot,result:t},t}())===s&&(o=null),o!==s?(a=o,r={type:"regexp",value:new RegExp(n.join(""),a?a.join(""):"")},t=r):(ot=t,t=s)):(ot=t,t=s)):(ot=t,t=s)}else ot=t,t=s;return it[c]={nextPos:ot,result:t},t}()),u!==s?(r=I(r,n,u),t=r):(ot=t,t=s)):(ot=t,t=s),t===s&&(t=ot,(r=Ct())!==s&&dt()!==s&&(n=function(){var t,r,n,u=30*ot+11,o=it[u];return o?(ot=o.nextPos,o.result):(t=ot,P.test(e.charAt(ot))?(r=e.charAt(ot),ot++):(r=s,vt(C)),r===s&&(r=null),r!==s?(61===e.charCodeAt(ot)?(n="=",ot++):(n=s,vt(w)),n!==s?(r=j(r),t=r):(ot=t,t=s)):(ot=t,t=s),t===s&&(E.test(e.charAt(ot))?(t=e.charAt(ot),ot++):(t=s,vt(S))),it[u]={nextPos:ot,result:t},t)}())!==s&&dt()!==s?((u=function(){var t,r,n,u,o,a,c=30*ot+15,i=it[c];if(i)return ot=i.nextPos,i.result;if(t=ot,34===e.charCodeAt(ot)?(r='"',ot++):(r=s,vt(T)),r!==s){for(n=[],F.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(K)),u===s&&(u=ot,92===e.charCodeAt(ot)?(o="\\",ot++):(o=s,vt(O)),o!==s?(e.length>ot?(a=e.charAt(ot),ot++):(a=s,vt(D)),a!==s?(o=L(o,a),u=o):(ot=u,u=s)):(ot=u,u=s));u!==s;)n.push(u),F.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(K)),u===s&&(u=ot,92===e.charCodeAt(ot)?(o="\\",ot++):(o=s,vt(O)),o!==s?(e.length>ot?(a=e.charAt(ot),ot++):(a=s,vt(D)),a!==s?(o=L(o,a),u=o):(ot=u,u=s)):(ot=u,u=s));n!==s?(34===e.charCodeAt(ot)?(u='"',ot++):(u=s,vt(T)),u!==s?(r=R(n),t=r):(ot=t,t=s)):(ot=t,t=s)}else ot=t,t=s;if(t===s)if(t=ot,39===e.charCodeAt(ot)?(r="'",ot++):(r=s,vt(M)),r!==s){for(n=[],U.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(_)),u===s&&(u=ot,92===e.charCodeAt(ot)?(o="\\",ot++):(o=s,vt(O)),o!==s?(e.length>ot?(a=e.charAt(ot),ot++):(a=s,vt(D)),a!==s?(o=L(o,a),u=o):(ot=u,u=s)):(ot=u,u=s));u!==s;)n.push(u),U.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(_)),u===s&&(u=ot,92===e.charCodeAt(ot)?(o="\\",ot++):(o=s,vt(O)),o!==s?(e.length>ot?(a=e.charAt(ot),ot++):(a=s,vt(D)),a!==s?(o=L(o,a),u=o):(ot=u,u=s)):(ot=u,u=s));n!==s?(39===e.charCodeAt(ot)?(u="'",ot++):(u=s,vt(M)),u!==s?(r=R(n),t=r):(ot=t,t=s)):(ot=t,t=s)}else ot=t,t=s;return it[c]={nextPos:ot,result:t},t}())===s&&(u=function(){var t,r,n,u,o,a,c,i=30*ot+16,l=it[i];if(l)return ot=l.nextPos,l.result;for(t=ot,r=ot,n=[],q.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(G));u!==s;)n.push(u),q.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(G));if(n!==s?(46===e.charCodeAt(ot)?(u=".",ot++):(u=s,vt(k)),u!==s?r=n=[n,u]:(ot=r,r=s)):(ot=r,r=s),r===s&&(r=null),r!==s){if(n=[],q.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(G)),u!==s)for(;u!==s;)n.push(u),q.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(G));else n=s;n!==s?(a=n,c=(o=r)?[].concat.apply([],o).join(""):"",r={type:"literal",value:parseFloat(c+a.join(""))},t=r):(ot=t,t=s)}else ot=t,t=s;return it[i]={nextPos:ot,result:t},t}())===s&&(u=function(){var t,e,r=30*ot+17,n=it[r];return n?(ot=n.nextPos,n.result):((e=At())!==s&&(e={type:"literal",value:e}),t=e,it[r]={nextPos:ot,result:t},t)}()),u!==s?(r=I(r,n,u),t=r):(ot=t,t=s)):(ot=t,t=s),t===s&&(t=ot,(r=Ct())!==s&&(r={type:"attribute",name:r}),t=r)),it[o]={nextPos:ot,result:t},t)}())!==s&&dt()!==s?(93===e.charCodeAt(ot)?(u="]",ot++):(u=s,vt(b)),u!==s?t=r=n:(ot=t,t=s)):(ot=t,t=s),it[o]={nextPos:ot,result:t},t)}())===s&&(t=function(){var t,r,n,u,o,a,c,i,l=30*ot+21,f=it[l];if(f)return ot=f.nextPos,f.result;if(t=ot,46===e.charCodeAt(ot)?(r=".",ot++):(r=s,vt(k)),r!==s)if((n=At())!==s){for(u=[],o=ot,46===e.charCodeAt(ot)?(a=".",ot++):(a=s,vt(k)),a!==s&&(c=At())!==s?o=a=[a,c]:(ot=o,o=s);o!==s;)u.push(o),o=ot,46===e.charCodeAt(ot)?(a=".",ot++):(a=s,vt(k)),a!==s&&(c=At())!==s?o=a=[a,c]:(ot=o,o=s);u!==s?(i=n,r={type:"field",name:u.reduce((function(t,e){return t+e[0]+e[1]}),i)},t=r):(ot=t,t=s)}else ot=t,t=s;else ot=t,t=s;return it[l]={nextPos:ot,result:t},t}())===s&&(t=function(){var t,r,n,u,o=30*ot+22,a=it[o];return a?(ot=a.nextPos,a.result):(t=ot,":not("===e.substr(ot,5)?(r=":not(",ot+=5):(r=s,vt(X)),r!==s&&dt()!==s&&(n=gt())!==s&&dt()!==s?(41===e.charCodeAt(ot)?(u=")",ot++):(u=s,vt($)),u!==s?t=r={type:"not",selectors:n}:(ot=t,t=s)):(ot=t,t=s),it[o]={nextPos:ot,result:t},t)}())===s&&(t=function(){var t,r,n,u,o=30*ot+23,a=it[o];return a?(ot=a.nextPos,a.result):(t=ot,":matches("===e.substr(ot,9)?(r=":matches(",ot+=9):(r=s,vt(Y)),r!==s&&dt()!==s&&(n=gt())!==s&&dt()!==s?(41===e.charCodeAt(ot)?(u=")",ot++):(u=s,vt($)),u!==s?t=r={type:"matches",selectors:n}:(ot=t,t=s)):(ot=t,t=s),it[o]={nextPos:ot,result:t},t)}())===s&&(t=function(){var t,r,n,u,o=30*ot+24,a=it[o];return a?(ot=a.nextPos,a.result):(t=ot,":has("===e.substr(ot,5)?(r=":has(",ot+=5):(r=s,vt(Z)),r!==s&&dt()!==s&&(n=gt())!==s&&dt()!==s?(41===e.charCodeAt(ot)?(u=")",ot++):(u=s,vt($)),u!==s?t=r={type:"has",selectors:n}:(ot=t,t=s)):(ot=t,t=s),it[o]={nextPos:ot,result:t},t)}())===s&&(t=function(){var t,r,n=30*ot+25,u=it[n];return u?(ot=u.nextPos,u.result):(":first-child"===e.substr(ot,12)?(r=":first-child",ot+=12):(r=s,vt(tt)),r!==s&&(r=wt(1)),t=r,it[n]={nextPos:ot,result:t},t)}())===s&&(t=function(){var t,r,n=30*ot+26,u=it[n];return u?(ot=u.nextPos,u.result):(":last-child"===e.substr(ot,11)?(r=":last-child",ot+=11):(r=s,vt(et)),r!==s&&(r=jt(1)),t=r,it[n]={nextPos:ot,result:t},t)}())===s&&(t=function(){var t,r,n,u,o,a=30*ot+27,c=it[a];if(c)return ot=c.nextPos,c.result;if(t=ot,":nth-child("===e.substr(ot,11)?(r=":nth-child(",ot+=11):(r=s,vt(rt)),r!==s)if(dt()!==s){if(n=[],q.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(G)),u!==s)for(;u!==s;)n.push(u),q.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(G));else n=s;n!==s&&(u=dt())!==s?(41===e.charCodeAt(ot)?(o=")",ot++):(o=s,vt($)),o!==s?(r=wt(parseInt(n.join(""),10)),t=r):(ot=t,t=s)):(ot=t,t=s)}else ot=t,t=s;else ot=t,t=s;return it[a]={nextPos:ot,result:t},t}())===s&&(t=function(){var t,r,n,u,o,a=30*ot+28,c=it[a];if(c)return ot=c.nextPos,c.result;if(t=ot,":nth-last-child("===e.substr(ot,16)?(r=":nth-last-child(",ot+=16):(r=s,vt(nt)),r!==s)if(dt()!==s){if(n=[],q.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(G)),u!==s)for(;u!==s;)n.push(u),q.test(e.charAt(ot))?(u=e.charAt(ot),ot++):(u=s,vt(G));else n=s;n!==s&&(u=dt())!==s?(41===e.charCodeAt(ot)?(o=")",ot++):(o=s,vt($)),o!==s?(r=jt(parseInt(n.join(""),10)),t=r):(ot=t,t=s)):(ot=t,t=s)}else ot=t,t=s;else ot=t,t=s;return it[a]={nextPos:ot,result:t},t}())===s&&(t=function(){var t,r,n,u=30*ot+29,o=it[u];return o?(ot=o.nextPos,o.result):(t=ot,58===e.charCodeAt(ot)?(r=":",ot++):(r=s,vt(ut)),r!==s&&(n=At())!==s?t=r={type:"class",name:n}:(ot=t,t=s),it[u]={nextPos:ot,result:t},t)}()),it[r]={nextPos:ot,result:t},t)}function Ct(){var t,r,n,u,o,a,c,i,l=30*ot+13,f=it[l];if(f)return ot=f.nextPos,f.result;if(t=ot,(r=At())!==s){for(n=[],u=ot,46===e.charCodeAt(ot)?(o=".",ot++):(o=s,vt(k)),o!==s&&(a=At())!==s?u=o=[o,a]:(ot=u,u=s);u!==s;)n.push(u),u=ot,46===e.charCodeAt(ot)?(o=".",ot++):(o=s,vt(k)),o!==s&&(a=At())!==s?u=o=[o,a]:(ot=u,u=s);n!==s?(c=r,i=n,t=r=[].concat.apply([c],i).join("")):(ot=t,t=s)}else ot=t,t=s;return it[l]={nextPos:ot,result:t},t}function wt(t){return{type:"nth-child",index:{type:"literal",value:t}}}function jt(t){return{type:"nth-last-child",index:{type:"literal",value:t}}}if((n=i())!==s&&ot===e.length)return n;throw n!==s&&ot<e.length&&vt({type:"end"}),u=ct,o=st<e.length?e.charAt(st):null,a=st<e.length?pt(st,st+1):pt(st,st),new t(t.buildMessage(u,o),u,o,a)}}}())}));function s(t,e){for(var r=0;r<e.length;++r){if(null==t)return t;t=t[e[r]]}return t}var c="function"==typeof WeakMap?new WeakMap:null;function i(t){if(null==t)return function(){return!0};if(null!=c){var e=c.get(t);return null!=e||(e=l(t),c.set(t,e)),e}return l(t)}function l(r){switch(r.type){case"wildcard":return function(){return!0};case"identifier":var n=r.value.toLowerCase();return function(t,e,r){var u=r&&r.nodeTypeKey||"type";return n===t[u].toLowerCase()};case"field":var u=r.name.split(".");return function(t,e){return function t(e,r,n,u){for(var o=r,a=u;a<n.length;++a){if(null==o)return!1;var s=o[n[a]];if(Array.isArray(s)){for(var c=0;c<s.length;++c)if(t(e,s[c],n,a+1))return!0;return!1}o=s}return e===o}(t,e[u.length-1],u,0)};case"matches":var o=r.selectors.map(i);return function(t,e,r){for(var n=0;n<o.length;++n)if(o[n](t,e,r))return!0;return!1};case"compound":var a=r.selectors.map(i);return function(t,e,r){for(var n=0;n<a.length;++n)if(!a[n](t,e,r))return!1;return!0};case"not":var c=r.selectors.map(i);return function(t,e,r){for(var n=0;n<c.length;++n)if(c[n](t,e,r))return!1;return!0};case"has":var l=r.selectors.map(i);return function(e,r,n){var u=!1,o=[];return t.traverse(e,{enter:function(t,e){null!=e&&o.unshift(e);for(var r=0;r<l.length;++r)if(l[r](t,o,n))return u=!0,void this.break()},leave:function(){o.shift()},keys:n&&n.visitorKeys,fallback:n&&n.fallback||"iteration"}),u};case"child":var f=i(r.left),h=i(r.right);return function(t,e,r){return!!(e.length>0&&h(t,e,r))&&f(e[0],e.slice(1),r)};case"descendant":var d=i(r.left),A=i(r.right);return function(t,e,r){if(A(t,e,r))for(var n=0,u=e.length;n<u;++n)if(d(e[n],e.slice(n+1),r))return!0;return!1};case"attribute":var x=r.name.split(".");switch(r.operator){case void 0:return function(t){return null!=s(t,x)};case"=":switch(r.value.type){case"regexp":return function(t){var e=s(t,x);return"string"==typeof e&&r.value.value.test(e)};case"literal":var g="".concat(r.value.value);return function(t){return g==="".concat(s(t,x))};case"type":return function(t){return r.value.value===e(s(t,x))}}throw new Error("Unknown selector value type: ".concat(r.value.type));case"!=":switch(r.value.type){case"regexp":return function(t){return!r.value.value.test(s(t,x))};case"literal":var m="".concat(r.value.value);return function(t){return m!=="".concat(s(t,x))};case"type":return function(t){return r.value.value!==e(s(t,x))}}throw new Error("Unknown selector value type: ".concat(r.value.type));case"<=":return function(t){return s(t,x)<=r.value.value};case"<":return function(t){return s(t,x)<r.value.value};case">":return function(t){return s(t,x)>r.value.value};case">=":return function(t){return s(t,x)>=r.value.value}}throw new Error("Unknown operator: ".concat(r.operator));case"sibling":var b=i(r.left),P=i(r.right);return function(t,e,n){return P(t,e,n)&&p(t,b,e,"LEFT_SIDE",n)||r.left.subject&&b(t,e,n)&&p(t,P,e,"RIGHT_SIDE",n)};case"adjacent":var C=i(r.left),w=i(r.right);return function(t,e,n){return w(t,e,n)&&v(t,C,e,"LEFT_SIDE",n)||r.right.subject&&C(t,e,n)&&v(t,w,e,"RIGHT_SIDE",n)};case"nth-child":var j=r.index.value,E=i(r.right);return function(t,e,r){return E(t,e,r)&&y(t,e,j,r)};case"nth-last-child":var S=-r.index.value,k=i(r.right);return function(t,e,r){return k(t,e,r)&&y(t,e,S,r)};case"class":return function(t,e,n){if(n&&n.matchClass)return n.matchClass(r.name,t,e);if(n&&n.nodeTypeKey)return!1;switch(r.name.toLowerCase()){case"statement":if("Statement"===t.type.slice(-9))return!0;case"declaration":return"Declaration"===t.type.slice(-11);case"pattern":if("Pattern"===t.type.slice(-7))return!0;case"expression":return"Expression"===t.type.slice(-10)||"Literal"===t.type.slice(-7)||"Identifier"===t.type&&(0===e.length||"MetaProperty"!==e[0].type)||"MetaProperty"===t.type;case"function":return"FunctionDeclaration"===t.type||"FunctionExpression"===t.type||"ArrowFunctionExpression"===t.type}throw new Error("Unknown class name: ".concat(r.name))}}throw new Error("Unknown selector type: ".concat(r.type))}function f(e,r){var n=r&&r.nodeTypeKey||"type",u=e[n];return r&&r.visitorKeys&&r.visitorKeys[u]?r.visitorKeys[u]:t.VisitorKeys[u]?t.VisitorKeys[u]:r&&"function"==typeof r.fallback?r.fallback(e):Object.keys(e).filter((function(t){return t!==n}))}function h(t,r){var n=r&&r.nodeTypeKey||"type";return null!==t&&"object"===e(t)&&"string"==typeof t[n]}function p(t,e,n,u,o){var a=r(n,1)[0];if(!a)return!1;for(var s=f(a,o),c=0;c<s.length;++c){var i=a[s[c]];if(Array.isArray(i)){var l=i.indexOf(t);if(l<0)continue;var p=void 0,v=void 0;"LEFT_SIDE"===u?(p=0,v=l):(p=l+1,v=i.length);for(var y=p;y<v;++y)if(h(i[y],o)&&e(i[y],n,o))return!0}}return!1}function v(t,e,n,u,o){var a=r(n,1)[0];if(!a)return!1;for(var s=f(a,o),c=0;c<s.length;++c){var i=a[s[c]];if(Array.isArray(i)){var l=i.indexOf(t);if(l<0)continue;if("LEFT_SIDE"===u&&l>0&&h(i[l-1],o)&&e(i[l-1],n,o))return!0;if("RIGHT_SIDE"===u&&l<i.length-1&&h(i[l+1],o)&&e(i[l+1],n,o))return!0}}return!1}function y(t,e,n,u){if(0===n)return!1;var o=r(e,1)[0];if(!o)return!1;for(var a=f(o,u),s=0;s<a.length;++s){var c=o[a[s]];if(Array.isArray(c)){var i=n<0?c.length+n:n-1;if(i>=0&&i<c.length&&c[i]===t)return!0}}return!1}function d(r,u,o,a){if(u){var s=[],c=i(u),l=function t(r,u){if(null==r||"object"!=e(r))return[];null==u&&(u=r);for(var o=r.subject?[u]:[],a=Object.keys(r),s=0;s<a.length;++s){var c=a[s],i=r[c];o.push.apply(o,n(t(i,"left"===c?i:u)))}return o}(u).map(i);t.traverse(r,{enter:function(t,e){if(null!=e&&s.unshift(e),c(t,s,a))if(l.length)for(var r=0,n=l.length;r<n;++r){l[r](t,s,a)&&o(t,e,s);for(var u=0,i=s.length;u<i;++u){var f=s.slice(u+1);l[r](s[u],f,a)&&o(s[u],e,f)}}else o(t,e,s)},leave:function(){s.shift()},keys:a&&a.visitorKeys,fallback:a&&a.fallback||"iteration"})}}function A(t,e,r){var n=[];return d(t,e,(function(t){n.push(t)}),r),n}function x(t){return a.parse(t)}function g(t,e,r){return A(t,x(e),r)}return g.parse=x,g.match=A,g.traverse=d,g.matches=function(t,e,r,n){return!e||!!t&&(r||(r=[]),i(e)(t,r,n))},g.query=g,g}));
//# sourceMappingURL=esquery.lite.min.js.map
