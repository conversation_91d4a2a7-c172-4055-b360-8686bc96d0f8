/**
 * React Router DOM v6.16.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-router"),require("@remix-run/router")):"function"==typeof define&&define.amd?define(["exports","react","react-router","@remix-run/router"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactRouterDOM={},e.<PERSON>,<PERSON><PERSON><PERSON>,e.<PERSON><PERSON><PERSON><PERSON>)}(this,(function(e,t,r,n){"use strict";function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=o(t);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},u.apply(this,arguments)}function i(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}const c="get",s="application/x-www-form-urlencoded";function l(e){return null!=e&&"string"==typeof e.tagName}function f(e){return void 0===e&&(e=""),new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce(((t,r)=>{let n=e[r];return t.concat(Array.isArray(n)?n.map((e=>[r,e])):[[r,n]])}),[]))}let d=null;const m=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function b(e){return null==e||m.has(e)?e:null}function p(e,t){let r,o,a,u,i;if(l(f=e)&&"form"===f.tagName.toLowerCase()){let i=e.getAttribute("action");o=i?n.stripBasename(i,t):null,r=e.getAttribute("method")||c,a=b(e.getAttribute("enctype"))||s,u=new FormData(e)}else if(function(e){return l(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return l(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||i.getAttribute("action");if(o=l?n.stripBasename(l,t):null,r=e.getAttribute("formmethod")||i.getAttribute("method")||c,a=b(e.getAttribute("formenctype"))||b(i.getAttribute("enctype"))||s,u=new FormData(i,e),!function(){if(null===d)try{new FormData(document.createElement("form"),0),d=!1}catch(e){d=!0}return d}()){let{name:t,type:r,value:n}=e;if("image"===r){let e=t?t+".":"";u.append(e+"x","0"),u.append(e+"y","0")}else t&&u.append(t,n)}}else{if(l(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=c,o=null,a=s,i=e}var f;return u&&"text/plain"===a&&(i=u,u=void 0),{action:o,method:r.toLowerCase(),encType:a,formData:u,body:i}}const y=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset"],g=["aria-current","caseSensitive","className","end","style","to","children"],h=["reloadDocument","replace","state","method","action","onSubmit","submit","relative","preventScrollReset"];function v(){var e;let t=null==(e=window)?void 0:e.__staticRouterHydrationData;return t&&t.errors&&(t=u({},t,{errors:R(t.errors)})),t}function R(e){if(!e)return null;let t=Object.entries(e),r={};for(let[e,o]of t)if(o&&"RouteErrorResponse"===o.__type)r[e]=new n.UNSAFE_ErrorResponseImpl(o.status,o.statusText,o.data,!0===o.internal);else if(o&&"Error"===o.__type){if(o.__subType){let t=window[o.__subType];if("function"==typeof t)try{let n=new t(o.message);n.stack="",r[e]=n}catch(e){}}if(null==r[e]){let t=new Error(o.message);t.stack="",r[e]=t}}else r[e]=o;return r}const w=a.startTransition;const P="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,S=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,E=a.forwardRef((function(e,t){let o,{onClick:c,relative:s,reloadDocument:l,replace:f,state:d,target:m,to:b,preventScrollReset:p}=e,g=i(e,y),{basename:h}=a.useContext(r.UNSAFE_NavigationContext),v=!1;if("string"==typeof b&&S.test(b)&&(o=b,P))try{let e=new URL(window.location.href),t=b.startsWith("//")?new URL(e.protocol+b):new URL(b),r=n.stripBasename(t.pathname,h);t.origin===e.origin&&null!=r?b=r+t.search+t.hash:v=!0}catch(e){}let R=r.useHref(b,{relative:s}),w=U(b,{replace:f,state:d,target:m,preventScrollReset:p,relative:s});return a.createElement("a",u({},g,{href:o||R,onClick:v||l?c:function(e){c&&c(e),e.defaultPrevented||w(e)},ref:t,target:m}))})),O=a.forwardRef((function(e,t){let{"aria-current":n="page",caseSensitive:o=!1,className:c="",end:s=!1,style:l,to:f,children:d}=e,m=i(e,g),b=r.useResolvedPath(f,{relative:m.relative}),p=r.useLocation(),y=a.useContext(r.UNSAFE_DataRouterStateContext),{navigator:h}=a.useContext(r.UNSAFE_NavigationContext),v=h.encodeLocation?h.encodeLocation(b).pathname:b.pathname,R=p.pathname,w=y&&y.navigation&&y.navigation.location?y.navigation.location.pathname:null;o||(R=R.toLowerCase(),w=w?w.toLowerCase():null,v=v.toLowerCase());let P,S=R===v||!s&&R.startsWith(v)&&"/"===R.charAt(v.length),O=null!=w&&(w===v||!s&&w.startsWith(v)&&"/"===w.charAt(v.length)),j=S?n:void 0;P="function"==typeof c?c({isActive:S,isPending:O}):[c,S?"active":null,O?"pending":null].filter(Boolean).join(" ");let N="function"==typeof l?l({isActive:S,isPending:O}):l;return a.createElement(E,u({},m,{"aria-current":j,className:P,ref:t,style:N,to:f}),"function"==typeof d?d({isActive:S,isPending:O}):d)})),j=a.forwardRef(((e,t)=>{let r=L();return a.createElement(N,u({},e,{submit:r,ref:t}))})),N=a.forwardRef(((e,t)=>{let{reloadDocument:r,replace:n,state:o,method:s=c,action:l,onSubmit:f,submit:d,relative:m,preventScrollReset:b}=e,p=i(e,h),y="get"===s.toLowerCase()?"get":"post",g=T(l,{relative:m});return a.createElement("form",u({ref:t,method:y,action:g,onSubmit:r?f:e=>{if(f&&f(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,r=(null==t?void 0:t.getAttribute("formmethod"))||s;d(t||e.currentTarget,{method:r,replace:n,state:o,relative:m,preventScrollReset:b})}},p))}));var A=function(e){return e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e}(A||{}),C=function(e){return e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration",e}(C||{});function _(e){let t=a.useContext(r.UNSAFE_DataRouterContext);return t||n.UNSAFE_invariant(!1),t}function F(e){let t=a.useContext(r.UNSAFE_DataRouterStateContext);return t||n.UNSAFE_invariant(!1),t}function U(e,t){let{target:n,replace:o,state:u,preventScrollReset:i,relative:c}=void 0===t?{}:t,s=r.useNavigate(),l=r.useLocation(),f=r.useResolvedPath(e,{relative:c});return a.useCallback((t=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(t,n)){t.preventDefault();let n=void 0!==o?o:r.createPath(l)===r.createPath(f);s(e,{replace:n,state:u,preventScrollReset:i,relative:c})}}),[l,s,f,o,u,n,e,i,c])}function x(){if("undefined"==typeof document)throw new Error("You are calling submit during the server render. Try calling submit within a `useEffect` or callback instead.")}function L(){let{router:e}=_(A.UseSubmit),{basename:t}=a.useContext(r.UNSAFE_NavigationContext),n=r.UNSAFE_useRouteId();return a.useCallback((function(r,o){void 0===o&&(o={}),x();let{action:a,method:u,encType:i,formData:c,body:s}=p(r,t);e.navigate(o.action||a,{preventScrollReset:o.preventScrollReset,formData:c,body:s,formMethod:o.method||u,formEncType:o.encType||i,replace:o.replace,state:o.state,fromRouteId:n})}),[e,t,n])}function D(e,t){let{router:o}=_(A.UseSubmitFetcher),{basename:u}=a.useContext(r.UNSAFE_NavigationContext);return a.useCallback((function(r,a){void 0===a&&(a={}),x();let{action:i,method:c,encType:s,formData:l,body:f}=p(r,u);null==t&&n.UNSAFE_invariant(!1),o.fetch(e,t,a.action||i,{preventScrollReset:a.preventScrollReset,formData:l,body:f,formMethod:a.method||c,formEncType:a.encType||s})}),[o,u,e,t])}function T(e,t){let{relative:o}=void 0===t?{}:t,{basename:i}=a.useContext(r.UNSAFE_NavigationContext),c=a.useContext(r.UNSAFE_RouteContext);c||n.UNSAFE_invariant(!1);let[s]=c.matches.slice(-1),l=u({},r.useResolvedPath(e||".",{relative:o})),f=r.useLocation();if(null==e&&(l.search=f.search,s.route.index)){let e=new URLSearchParams(l.search);e.delete("index"),l.search=e.toString()?"?"+e.toString():""}return e&&"."!==e||!s.route.index||(l.search=l.search?l.search.replace(/^\?/,"?index&"):"?index"),"/"!==i&&(l.pathname="/"===l.pathname?i:n.joinPaths([i,l.pathname])),r.createPath(l)}let k=0;const M="react-router-scroll-positions";let B={};function H(e){let{getKey:t,storageKey:o}=void 0===e?{}:e,{router:i}=_(A.UseScrollRestoration),{restoreScrollPosition:c,preventScrollReset:s}=F(C.UseScrollRestoration),{basename:l}=a.useContext(r.UNSAFE_NavigationContext),f=r.useLocation(),d=r.useMatches(),m=r.useNavigation();a.useEffect((()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"})),[]),function(e,t){let{capture:r}=t||{};a.useEffect((()=>{let t=null!=r?{capture:r}:void 0;return window.addEventListener("pagehide",e,t),()=>{window.removeEventListener("pagehide",e,t)}}),[e,r])}(a.useCallback((()=>{if("idle"===m.state){let e=(t?t(f,d):null)||f.key;B[e]=window.scrollY}sessionStorage.setItem(o||M,JSON.stringify(B)),window.history.scrollRestoration="auto"}),[o,t,m.state,f,d])),"undefined"!=typeof document&&(a.useLayoutEffect((()=>{try{let e=sessionStorage.getItem(o||M);e&&(B=JSON.parse(e))}catch(e){}}),[o]),a.useLayoutEffect((()=>{let e=t&&"/"!==l?(e,r)=>t(u({},e,{pathname:n.stripBasename(e.pathname,l)||e.pathname}),r):t,r=null==i?void 0:i.enableScrollRestoration(B,(()=>window.scrollY),e);return()=>r&&r()}),[i,l,t]),a.useLayoutEffect((()=>{if(!1!==c)if("number"!=typeof c){if(f.hash){let e=document.getElementById(decodeURIComponent(f.hash.slice(1)));if(e)return void e.scrollIntoView()}!0!==s&&window.scrollTo(0,0)}else window.scrollTo(0,c)}),[f,c,s]))}Object.defineProperty(e,"AbortedDeferredError",{enumerable:!0,get:function(){return r.AbortedDeferredError}}),Object.defineProperty(e,"Await",{enumerable:!0,get:function(){return r.Await}}),Object.defineProperty(e,"MemoryRouter",{enumerable:!0,get:function(){return r.MemoryRouter}}),Object.defineProperty(e,"Navigate",{enumerable:!0,get:function(){return r.Navigate}}),Object.defineProperty(e,"NavigationType",{enumerable:!0,get:function(){return r.NavigationType}}),Object.defineProperty(e,"Outlet",{enumerable:!0,get:function(){return r.Outlet}}),Object.defineProperty(e,"Route",{enumerable:!0,get:function(){return r.Route}}),Object.defineProperty(e,"Router",{enumerable:!0,get:function(){return r.Router}}),Object.defineProperty(e,"RouterProvider",{enumerable:!0,get:function(){return r.RouterProvider}}),Object.defineProperty(e,"Routes",{enumerable:!0,get:function(){return r.Routes}}),Object.defineProperty(e,"UNSAFE_DataRouterContext",{enumerable:!0,get:function(){return r.UNSAFE_DataRouterContext}}),Object.defineProperty(e,"UNSAFE_DataRouterStateContext",{enumerable:!0,get:function(){return r.UNSAFE_DataRouterStateContext}}),Object.defineProperty(e,"UNSAFE_LocationContext",{enumerable:!0,get:function(){return r.UNSAFE_LocationContext}}),Object.defineProperty(e,"UNSAFE_NavigationContext",{enumerable:!0,get:function(){return r.UNSAFE_NavigationContext}}),Object.defineProperty(e,"UNSAFE_RouteContext",{enumerable:!0,get:function(){return r.UNSAFE_RouteContext}}),Object.defineProperty(e,"UNSAFE_useRouteId",{enumerable:!0,get:function(){return r.UNSAFE_useRouteId}}),Object.defineProperty(e,"createMemoryRouter",{enumerable:!0,get:function(){return r.createMemoryRouter}}),Object.defineProperty(e,"createPath",{enumerable:!0,get:function(){return r.createPath}}),Object.defineProperty(e,"createRoutesFromChildren",{enumerable:!0,get:function(){return r.createRoutesFromChildren}}),Object.defineProperty(e,"createRoutesFromElements",{enumerable:!0,get:function(){return r.createRoutesFromElements}}),Object.defineProperty(e,"defer",{enumerable:!0,get:function(){return r.defer}}),Object.defineProperty(e,"generatePath",{enumerable:!0,get:function(){return r.generatePath}}),Object.defineProperty(e,"isRouteErrorResponse",{enumerable:!0,get:function(){return r.isRouteErrorResponse}}),Object.defineProperty(e,"json",{enumerable:!0,get:function(){return r.json}}),Object.defineProperty(e,"matchPath",{enumerable:!0,get:function(){return r.matchPath}}),Object.defineProperty(e,"matchRoutes",{enumerable:!0,get:function(){return r.matchRoutes}}),Object.defineProperty(e,"parsePath",{enumerable:!0,get:function(){return r.parsePath}}),Object.defineProperty(e,"redirect",{enumerable:!0,get:function(){return r.redirect}}),Object.defineProperty(e,"redirectDocument",{enumerable:!0,get:function(){return r.redirectDocument}}),Object.defineProperty(e,"renderMatches",{enumerable:!0,get:function(){return r.renderMatches}}),Object.defineProperty(e,"resolvePath",{enumerable:!0,get:function(){return r.resolvePath}}),Object.defineProperty(e,"unstable_useBlocker",{enumerable:!0,get:function(){return r.unstable_useBlocker}}),Object.defineProperty(e,"useActionData",{enumerable:!0,get:function(){return r.useActionData}}),Object.defineProperty(e,"useAsyncError",{enumerable:!0,get:function(){return r.useAsyncError}}),Object.defineProperty(e,"useAsyncValue",{enumerable:!0,get:function(){return r.useAsyncValue}}),Object.defineProperty(e,"useHref",{enumerable:!0,get:function(){return r.useHref}}),Object.defineProperty(e,"useInRouterContext",{enumerable:!0,get:function(){return r.useInRouterContext}}),Object.defineProperty(e,"useLoaderData",{enumerable:!0,get:function(){return r.useLoaderData}}),Object.defineProperty(e,"useLocation",{enumerable:!0,get:function(){return r.useLocation}}),Object.defineProperty(e,"useMatch",{enumerable:!0,get:function(){return r.useMatch}}),Object.defineProperty(e,"useMatches",{enumerable:!0,get:function(){return r.useMatches}}),Object.defineProperty(e,"useNavigate",{enumerable:!0,get:function(){return r.useNavigate}}),Object.defineProperty(e,"useNavigation",{enumerable:!0,get:function(){return r.useNavigation}}),Object.defineProperty(e,"useNavigationType",{enumerable:!0,get:function(){return r.useNavigationType}}),Object.defineProperty(e,"useOutlet",{enumerable:!0,get:function(){return r.useOutlet}}),Object.defineProperty(e,"useOutletContext",{enumerable:!0,get:function(){return r.useOutletContext}}),Object.defineProperty(e,"useParams",{enumerable:!0,get:function(){return r.useParams}}),Object.defineProperty(e,"useResolvedPath",{enumerable:!0,get:function(){return r.useResolvedPath}}),Object.defineProperty(e,"useRevalidator",{enumerable:!0,get:function(){return r.useRevalidator}}),Object.defineProperty(e,"useRouteError",{enumerable:!0,get:function(){return r.useRouteError}}),Object.defineProperty(e,"useRouteLoaderData",{enumerable:!0,get:function(){return r.useRouteLoaderData}}),Object.defineProperty(e,"useRoutes",{enumerable:!0,get:function(){return r.useRoutes}}),e.BrowserRouter=function(e){let{basename:t,children:o,future:u,window:i}=e,c=a.useRef();null==c.current&&(c.current=n.createBrowserHistory({window:i,v5Compat:!0}));let s=c.current,[l,f]=a.useState({action:s.action,location:s.location}),{v7_startTransition:d}=u||{},m=a.useCallback((e=>{d&&w?w((()=>f(e))):f(e)}),[f,d]);return a.useLayoutEffect((()=>s.listen(m)),[s,m]),a.createElement(r.Router,{basename:t,children:o,location:l.location,navigationType:l.action,navigator:s})},e.Form=j,e.HashRouter=function(e){let{basename:t,children:o,future:u,window:i}=e,c=a.useRef();null==c.current&&(c.current=n.createHashHistory({window:i,v5Compat:!0}));let s=c.current,[l,f]=a.useState({action:s.action,location:s.location}),{v7_startTransition:d}=u||{},m=a.useCallback((e=>{d&&w?w((()=>f(e))):f(e)}),[f,d]);return a.useLayoutEffect((()=>s.listen(m)),[s,m]),a.createElement(r.Router,{basename:t,children:o,location:l.location,navigationType:l.action,navigator:s})},e.Link=E,e.NavLink=O,e.ScrollRestoration=function(e){let{getKey:t,storageKey:r}=e;return H({getKey:t,storageKey:r}),null},e.UNSAFE_useScrollRestoration=H,e.createBrowserRouter=function(e,t){return n.createRouter({basename:null==t?void 0:t.basename,future:u({},null==t?void 0:t.future,{v7_prependBasename:!0}),history:n.createBrowserHistory({window:null==t?void 0:t.window}),hydrationData:(null==t?void 0:t.hydrationData)||v(),routes:e,mapRouteProperties:r.UNSAFE_mapRouteProperties}).initialize()},e.createHashRouter=function(e,t){return n.createRouter({basename:null==t?void 0:t.basename,future:u({},null==t?void 0:t.future,{v7_prependBasename:!0}),history:n.createHashHistory({window:null==t?void 0:t.window}),hydrationData:(null==t?void 0:t.hydrationData)||v(),routes:e,mapRouteProperties:r.UNSAFE_mapRouteProperties}).initialize()},e.createSearchParams=f,e.unstable_HistoryRouter=function(e){let{basename:t,children:n,future:o,history:u}=e,[i,c]=a.useState({action:u.action,location:u.location}),{v7_startTransition:s}=o||{},l=a.useCallback((e=>{s&&w?w((()=>c(e))):c(e)}),[c,s]);return a.useLayoutEffect((()=>u.listen(l)),[u,l]),a.createElement(r.Router,{basename:t,children:n,location:i.location,navigationType:i.action,navigator:u})},e.unstable_usePrompt=function(e){let{when:t,message:n}=e,o=r.unstable_useBlocker(t);a.useEffect((()=>{if("blocked"===o.state){window.confirm(n)?setTimeout(o.proceed,0):o.reset()}}),[o,n]),a.useEffect((()=>{"blocked"!==o.state||t||o.reset()}),[o,t])},e.useBeforeUnload=function(e,t){let{capture:r}=t||{};a.useEffect((()=>{let t=null!=r?{capture:r}:void 0;return window.addEventListener("beforeunload",e,t),()=>{window.removeEventListener("beforeunload",e,t)}}),[e,r])},e.useFetcher=function(){var e;let{router:t}=_(A.UseFetcher),o=a.useContext(r.UNSAFE_RouteContext);o||n.UNSAFE_invariant(!1);let i=null==(e=o.matches[o.matches.length-1])?void 0:e.route.id;null==i&&n.UNSAFE_invariant(!1);let[c]=a.useState((()=>String(++k))),[s]=a.useState((()=>(i||n.UNSAFE_invariant(!1),function(e,t){return a.forwardRef(((r,n)=>{let o=D(e,t);return a.createElement(N,u({},r,{ref:n,submit:o}))}))}(c,i)))),[l]=a.useState((()=>e=>{t||n.UNSAFE_invariant(!1),i||n.UNSAFE_invariant(!1),t.fetch(c,i,e)})),f=D(c,i),d=t.getFetcher(c),m=a.useMemo((()=>u({Form:s,submit:f,load:l},d)),[d,s,f,l]);return a.useEffect((()=>()=>{t?t.deleteFetcher(c):console.warn("No router available to clean up from useFetcher()")}),[t,c]),m},e.useFetchers=function(){return[...F(C.UseFetchers).fetchers.values()]},e.useFormAction=T,e.useLinkClickHandler=U,e.useSearchParams=function(e){let t=a.useRef(f(e)),n=a.useRef(!1),o=r.useLocation(),u=a.useMemo((()=>function(e,t){let r=f(e);return t&&t.forEach(((e,n)=>{r.has(n)||t.getAll(n).forEach((e=>{r.append(n,e)}))})),r}(o.search,n.current?null:t.current)),[o.search]),i=r.useNavigate(),c=a.useCallback(((e,t)=>{const r=f("function"==typeof e?e(u):e);n.current=!0,i("?"+r,t)}),[i,u]);return[u,c]},e.useSubmit=L,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react-router-dom.production.min.js.map
